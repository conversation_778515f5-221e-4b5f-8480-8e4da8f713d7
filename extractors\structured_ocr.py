import base64
import json
from pathlib import Path
from typing import List, Dict, Any
from pydantic import BaseModel, Field
from mistralai import Mistral, DocumentURLChunk, ImageURLChunk, TextChunk
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class StructuredOCR(BaseModel):
    """
    Pydantic model for structured OCR output based on Mistral cookbook.
    """
    file_name: str = Field(description="Name of the processed file")
    topics: List[str] = Field(description="List of topics/themes found in the document")
    languages: str = Field(description="Primary language of the document")
    ocr_contents: Dict[str, Any] = Field(description="Structured OCR content as dictionary")
    document_type: str = Field(default="", description="Detected document type")
    confidence_score: float = Field(default=0.0, description="Confidence score for extraction")


class EnhancedOCRProcessor:
    """
    Enhanced OCR processor implementing the Mistral cookbook pattern with improvements.
    """
    
    def __init__(self, api_key: str):
        """
        Initialize the OCR processor.
        
        Args:
            api_key (str): Mistral API key
        """
        self.client = Mistral(api_key=api_key)
        self.supported_formats = {'.pdf', '.jpg', '.jpeg', '.png'}
    
    def process_document(self, document_path: str, include_images: bool = False) -> StructuredOCR:
        """
        Process a document using OCR and extract structured data.
        
        Args:
            document_path (str): Path to the document file
            include_images (bool): Whether to include base64 images in response
            
        Returns:
            StructuredOCR: Structured OCR result
            
        Raises:
            AssertionError: If the document file does not exist
            ValueError: If the document format is not supported
        """
        # Validate input file
        document_file = Path(document_path)
        if not document_file.is_file():
            raise AssertionError(f"The provided document path does not exist: {document_path}")
        
        if document_file.suffix.lower() not in self.supported_formats:
            raise ValueError(f"Unsupported file format: {document_file.suffix}")
        
        try:
            # Process based on file type
            if document_file.suffix.lower() == '.pdf':
                return self._process_pdf(document_path, include_images)
            else:
                return self._process_image(document_path, include_images)
                
        except Exception as e:
            logger.error(f"Error processing document {document_path}: {e}")
            # Return empty structured result on error
            return StructuredOCR(
                file_name=document_file.name,
                topics=[],
                languages="unknown",
                ocr_contents={"error": str(e)},
                document_type="error",
                confidence_score=0.0
            )
    
    def _process_pdf(self, pdf_path: str, include_images: bool = False) -> StructuredOCR:
        """
        Process a PDF file using Mistral OCR.
        
        Args:
            pdf_path (str): Path to the PDF file
            include_images (bool): Whether to include images
            
        Returns:
            StructuredOCR: Structured OCR result
        """
        pdf_file = Path(pdf_path)
        
        # Upload PDF file to Mistral's OCR service
        with open(pdf_path, "rb") as f:
            uploaded_file = self.client.files.upload(
                file={
                    "file_name": pdf_file.stem,
                    "content": f.read(),
                },
                purpose="ocr",
            )
        
        # Get URL for the uploaded file
        signed_url = self.client.files.get_signed_url(file_id=uploaded_file.id, expiry=1)
        
        # Process PDF with OCR
        pdf_response = self.client.ocr.process(
            document=DocumentURLChunk(document_url=signed_url.url),
            model="mistral-ocr-latest",
            include_image_base64=include_images
        )
        
        # Extract markdown from all pages
        markdown_content = "\n\n".join([
            page.markdown for page in pdf_response.pages 
            if hasattr(page, "markdown") and page.markdown
        ])
        
        return self._extract_structured_data(pdf_file.name, markdown_content)
    
    def _process_image(self, image_path: str, include_images: bool = False) -> StructuredOCR:
        """
        Process an image file using Mistral OCR.
        
        Args:
            image_path (str): Path to the image file
            include_images (bool): Whether to include images
            
        Returns:
            StructuredOCR: Structured OCR result
        """
        image_file = Path(image_path)
        
        # Read and encode the image file
        with open(image_path, "rb") as f:
            encoded_image = base64.b64encode(f.read()).decode()
        
        base64_data_url = f"data:image/jpeg;base64,{encoded_image}"
        
        # Process the image using OCR
        image_response = self.client.ocr.process(
            document=ImageURLChunk(image_url=base64_data_url),
            model="mistral-ocr-latest"
        )
        
        # Extract markdown from the first page
        markdown_content = ""
        if image_response.pages and hasattr(image_response.pages[0], "markdown"):
            markdown_content = image_response.pages[0].markdown
        
        return self._extract_structured_data(image_file.name, markdown_content)
    
    def _extract_structured_data(self, file_name: str, markdown_content: str) -> StructuredOCR:
        """
        Extract structured data from OCR markdown content using LLM.
        
        Args:
            file_name (str): Name of the processed file
            markdown_content (str): OCR markdown content
            
        Returns:
            StructuredOCR: Structured OCR result
        """
        if not markdown_content.strip():
            return StructuredOCR(
                file_name=file_name,
                topics=[],
                languages="unknown",
                ocr_contents={"error": "No text content extracted"},
                document_type="empty",
                confidence_score=0.0
            )
        
        try:
            # Use chat.parse for structured output
            chat_response = self.client.chat.parse(
                model="pixtral-12b-latest",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            TextChunk(text=(
                                f"This is the document's OCR content in markdown:\n{markdown_content}\n\n"
                                "Convert this into a structured JSON response with the OCR contents "
                                "organized in a sensible dictionary format. Identify the main topics, "
                                "language, and structure the content logically."
                            ))
                        ]
                    }
                ],
                response_format=StructuredOCR,
                temperature=0
            )
            
            result = chat_response.choices[0].message.parsed
            result.file_name = file_name
            result.confidence_score = 0.9  # High confidence for successful parsing
            
            return result
            
        except Exception as e:
            logger.error(f"Error in structured extraction for {file_name}: {e}")
            # Fallback to basic structure
            return StructuredOCR(
                file_name=file_name,
                topics=["document"],
                languages="spanish",  # Default for Mexican documents
                ocr_contents={"raw_text": markdown_content},
                document_type="unknown",
                confidence_score=0.5
            )


def structured_ocr(document_path: str, api_key: str, include_images: bool = False) -> StructuredOCR:
    """
    Convenience function to process a document using structured OCR.
    
    Args:
        document_path (str): Path to the document file
        api_key (str): Mistral API key
        include_images (bool): Whether to include images in response
        
    Returns:
        StructuredOCR: Structured OCR result
    """
    processor = EnhancedOCRProcessor(api_key)
    return processor.process_document(document_path, include_images)


def batch_structured_ocr(document_paths: List[str], api_key: str) -> List[StructuredOCR]:
    """
    Process multiple documents using structured OCR.
    
    Args:
        document_paths (List[str]): List of document paths
        api_key (str): Mistral API key
        
    Returns:
        List[StructuredOCR]: List of structured OCR results
    """
    processor = EnhancedOCRProcessor(api_key)
    results = []
    
    for doc_path in document_paths:
        try:
            result = processor.process_document(doc_path)
            results.append(result)
            logger.info(f"Successfully processed: {doc_path}")
        except Exception as e:
            logger.error(f"Failed to process {doc_path}: {e}")
            # Add error result
            results.append(StructuredOCR(
                file_name=Path(doc_path).name,
                topics=[],
                languages="unknown",
                ocr_contents={"error": str(e)},
                document_type="error",
                confidence_score=0.0
            ))
    
    return results
