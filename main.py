import os
from pathlib import Path
from ocr import ocr_mistral, ocr_mistral_enhanced, is_zip_file
from extraction import extraer_campos_llm
from collections import defaultdict
import pandas as pd
import streamlit as st
from utils.zip_handler import ZipHandler

"""
Script principal para procesar documentos de empleados mexicanos.
- Aplica OCR y extracción semántica a múltiples archivos.
- Consolida los datos por colaborador.
- Exporta los resultados a un archivo Excel.
"""

# Lista de archivos de ejemplo (puedes cambiar por los archivos reales)
archivos = [
    "ActadeNacimiento.pdf",
    "NSS.pdf",
    "AvisodeRetencion.pdf",
    "ComprobanteDomicilio.pdf",
    "SAT.pdf",
    "INE.pdf",
    "Cuenta.jpg"
]

# Layout objetivo de la base de datos de empleados
LAYOUT_CAMPOS = [
    "id_colaborador",
    "Apellido_Paterno",
    "Apellido_Materno",
    "Nombre(s)",
    "Fecha_de_Nacimiento",
    "CURP",
    "RFC",
    "NSS",
    "Domicilio",
    "Colonia",
    "C.P.",
    "Municipio",
    "Estado",
    "Numero_Credito_Infonavit",
    "Tipo_Descuento",
    "Factor_Descuento",
    "Banco",
    "Cuenta",
    "Cuenta_Clabe"
]

# Prioridad de los tipos de documento para ciertos campos clave
PRIORIDAD_CAMPOS = {
    "Domicilio": ["comprobante_domicilio", "sat", "ine", "acta", "nss", "aviso_retencion"],
    "Colonia": ["comprobante_domicilio", "sat", "ine", "acta", "nss", "aviso_retencion"],
    "C.P.": ["comprobante_domicilio", "sat", "ine", "acta", "nss", "aviso_retencion"],
    "Municipio": ["comprobante_domicilio", "sat", "ine", "acta", "nss", "aviso_retencion"],
    "Estado": ["comprobante_domicilio", "sat", "ine", "acta", "nss", "aviso_retencion"],
    # Puedes agregar más campos con prioridad si lo deseas
}

CAMPOS_DOMICILIO = ["Domicilio", "Colonia", "C.P.", "Municipio", "Estado"]

def detectar_tipo_documento(nombre_archivo):
    """
    Detecta el tipo de documento a partir del nombre del archivo.
    Args:
        nombre_archivo (str): Nombre del archivo.
    Returns:
        str: Tipo de documento (acta, nss, aviso_retencion, etc.)
    """
    nombre = nombre_archivo.lower()
    if "acta" in nombre:
        return "acta"
    elif "nss" in nombre:
        return "nss"
    elif "aviso" in nombre:
        return "aviso_retencion"
    elif "comprobante" in nombre:
        return "comprobante_domicilio"
    elif "sat" in nombre:
        return "sat"
    elif "ine" in nombre:
        return "ine"
    elif "cuenta" in nombre:
        return "cuenta"
    else:
        return "otro"

def procesar_archivos(lista_archivos, api_key, use_structured=False):
    """
    Procesa una lista de archivos aplicando OCR y extracción semántica.
    Soporta archivos individuales y archivos ZIP.
    Args:
        lista_archivos (list): Lista de rutas de archivos.
        api_key (str): API Key de Mistral a usar para OCR.
        use_structured (bool): Si usar OCR estructurado.
    Returns:
        list: Resultados de extracción por archivo.
    """
    resultados = []
    zip_handler = ZipHandler()

    try:
        for archivo in lista_archivos:
            print(f"Procesando: {archivo}")

            # Check if it's a ZIP file
            if is_zip_file(archivo):
                print(f"📦 Procesando archivo ZIP: {archivo}")

                # Process ZIP file
                zip_results = ocr_mistral_enhanced(archivo, api_key=api_key, use_structured=use_structured)

                # Convert ZIP results to the expected format
                for zip_result in zip_results:
                    if zip_result['success']:
                        if use_structured and 'structured_data' in zip_result:
                            # Handle structured OCR results
                            structured_data = zip_result['structured_data']
                            texto = str(structured_data.ocr_contents)
                        else:
                            texto = zip_result.get('text_content', '')

                        if zip_result['file_name'].lower().startswith("acta"):
                            print(f"\nTEXTO COMPLETO EXTRAÍDO DEL ACTA DE NACIMIENTO:\n{'='*60}\n{texto}\n{'='*60}\n")
                        else:
                            print(f"Texto extraído de {zip_result['file_name']} (primeros 1000 caracteres):\n{texto[:1000]}\n{'-'*60}")

                        tipo = zip_result['document_type']
                        resultado = extraer_campos_llm(texto, tipo, api_key=api_key)
                        print(f"DEBUG resultado extractor para {zip_result['file_name']}: {resultado}")

                        resultados.append({
                            "archivo": zip_result['file_name'],
                            "tipo": tipo,
                            "id_colaborador": resultado.get("id_colaborador", ""),
                            "campos": resultado.get("campos", {})
                        })
                    else:
                        print(f"⚠️ Error procesando {zip_result['file_name']}: {zip_result['error']}")
            else:
                # Process individual file
                if use_structured:
                    structured_result = ocr_mistral_enhanced(archivo, api_key=api_key, use_structured=True)
                    texto = str(structured_result.ocr_contents)
                else:
                    texto = ocr_mistral(archivo, api_key=api_key)

                if archivo.lower().startswith("acta"):
                    print(f"\nTEXTO COMPLETO EXTRAÍDO DEL ACTA DE NACIMIENTO:\n{'='*60}\n{texto}\n{'='*60}\n")
                else:
                    print(f"Texto extraído de {archivo} (primeros 1000 caracteres):\n{texto[:1000]}\n{'-'*60}")

                tipo = detectar_tipo_documento(archivo)
                resultado = extraer_campos_llm(texto, tipo, api_key=api_key)
                print(f"DEBUG resultado extractor para {archivo}: {resultado}")

                resultados.append({
                    "archivo": archivo,
                    "tipo": tipo,
                    "id_colaborador": resultado.get("id_colaborador", ""),
                    "campos": resultado.get("campos", {})
                })

    except Exception as e:
        print(f"❌ Error durante el procesamiento: {str(e)}")
    finally:
        # Clean up ZIP handler resources
        zip_handler.cleanup()

    return resultados

def ensamblar_por_colaborador(resultados):
    """
    Agrupa los resultados por id_colaborador, para facilitar la consolidación.
    Args:
        resultados (list): Resultados de extracción por archivo.
    Returns:
        dict: Resultados agrupados por colaborador.
    """
    agrupados = defaultdict(dict)
    for res in resultados:
        id_col = res["id_colaborador"] if res["id_colaborador"] else "no_vinculado"
        agrupados[id_col][res["tipo"]] = {
            "archivo": res["archivo"],
            **res["campos"]
        }
    return dict(agrupados)

def consolidar_empleados(expedientes):
    """
    Consolida los datos de los expedientes agrupados en un DataFrame ancho, priorizando los campos según el tipo de documento.
    Realiza fusión de filas por nombre si hay duplicados sin CURP.
    Args:
        expedientes (dict): Expedientes agrupados por colaborador.
    Returns:
        pd.DataFrame: DataFrame consolidado de empleados.
    """
    empleados = []
    for id_col, docs in expedientes.items():
        fila = {campo: "" for campo in LAYOUT_CAMPOS}
        fila["id_colaborador"] = id_col
        for campo in LAYOUT_CAMPOS:
            prioridad = PRIORIDAD_CAMPOS.get(campo, docs.keys())
            for tipo in prioridad:
                if tipo in docs and campo in docs[tipo] and docs[tipo][campo]:
                    fila[campo] = docs[tipo][campo]
                    break  # Ya llené este campo con la mejor fuente
        empleados.append(fila)
    df = pd.DataFrame(empleados)

    # --- FUSIÓN POR NOMBRE EXACTO ---
    # Si una fila tiene CURP y otra solo nombre, y el nombre completo coincide, fusiona los datos de domicilio
    def nombre_completo(row):
        return f"{row['Nombre(s)']} {row['Apellido_Paterno']} {row['Apellido_Materno']}".strip().upper()

    # Índices para buscar coincidencias
    df_con_curp = df[df["CURP"] != ""].copy()
    df_sin_curp = df[(df["CURP"] == "") & (df["Nombre(s)"] != "")].copy()
    usados = set()
    for idx_sin, row_sin in df_sin_curp.iterrows():
        nombre_sin = nombre_completo(row_sin)
        for idx_con, row_con in df_con_curp.iterrows():
            nombre_con = nombre_completo(row_con)
            if nombre_sin == nombre_con:
                # Fusiona los campos de domicilio
                for campo in CAMPOS_DOMICILIO:
                    if row_sin[campo]:
                        df.at[idx_con, campo] = row_sin[campo]
                usados.add(idx_sin)
                break
    # Elimina las filas fusionadas (solo nombre)
    df = df.drop(list(usados)).reset_index(drop=True)
    return df

if __name__ == "__main__":
    # API Key por defecto incluida para facilidad de uso
    api_key_default = "swUM1obVjv5AVlD5BhWImQMj49HXm3jX"

    print("🔑 API Key de Mistral")
    print(f"Por defecto se usará la API Key incluida: {api_key_default[:20]}...")
    print("¿Quieres usar tu propia API Key? (Presiona Enter para usar la incluida)")

    api_key_input = input("Tu API Key (opcional): ").strip()

    if api_key_input:
        api_key_usuario = api_key_input
        print("✅ Usando tu API Key personalizada")
    else:
        api_key_usuario = api_key_default
        print("✅ Usando API Key incluida - ¡Listo para usar!")

    # Preguntar si usar OCR estructurado
    use_structured = input("¿Usar OCR estructurado? (s/N): ").strip().lower() in ['s', 'si', 'sí', 'y', 'yes']

    # Procesa los archivos y extrae los campos clave
    resultados = procesar_archivos(archivos, api_key=api_key_usuario, use_structured=use_structured)
    print("\nResultados extraídos:")
    for resultado in resultados:
        print(f"\nArchivo: {resultado['archivo']} (Tipo: {resultado['tipo']})")
        print(f"ID Colaborador: {resultado['id_colaborador']}")
        print(resultado["campos"])

    print("\n\n=== EXPEDIENTES AGRUPADOS POR COLABORADOR ===")
    expedientes = ensamblar_por_colaborador(resultados)
    for id_col, docs in expedientes.items():
        print(f"\nID Colaborador: {id_col}")
        for tipo, info in docs.items():
            print(f"  - {tipo}: {info}")

    # Consolidar empleados en DataFrame ancho
    df_empleados = consolidar_empleados(expedientes)
    print("\n\n=== DATAFRAME CONSOLIDADO DE EMPLEADOS ===")
    print(df_empleados)

    # Exportar a Excel
    df_empleados.to_excel("expedientes_consolidados.xlsx", index=False)
    print("\nArchivo 'expedientes_consolidados.xlsx' guardado correctamente.")

    with st.sidebar.expander("🔒 Aviso de Privacidad"):
        st.markdown("""
        ### Aviso de Privacidad

        En **Desempapelador RH** la privacidad y seguridad de tu información es una prioridad.

        - **Tus archivos solo existen durante tu sesión.**  
          Los documentos que subes se procesan de forma temporal y se eliminan automáticamente después de usarse. No se guardan en ningún servidor ni base de datos.

        - **Nadie más puede ver tus archivos ni tus resultados.**  
          Cada usuario tiene su propia sesión aislada. Otros usuarios no pueden acceder a tus documentos ni a tus datos, aunque estén usando la app al mismo tiempo.

        - **Tu API Key de Mistral es privada.**  
          La clave que ingresas solo se utiliza en tu sesión y nunca se almacena ni se comparte.

        - **El código fuente está en un repositorio privado.**  
          Solo el responsable de la app tiene acceso al código y a la configuración.

        - **Solo el proveedor de la nube (Streamlit Cloud) podría, en teoría, acceder a los archivos temporales mientras existen.**  
          Esto es una limitación técnica de cualquier servicio en la nube, pero se confía en las buenas prácticas de privacidad del proveedor.

        **Si tienes dudas sobre la privacidad de tus datos, puedes contactarme directamente:**  
        📧 <EMAIL>
        """) 