import os
import json
from mistralai import Mistral
from extractors.utils import extraer_json_de_llm

CAMPOS = [
    "Nombre(s)",
    "Apellido_Paterno",
    "Apellido_Materno",
    "CURP",
    "<PERSON>lave_Elector",
    "CIC",
    "<PERSON>ici<PERSON>",
    "Colonia",
    "C.P.",
    "Municipio",
    "Estado"
]

PROMPT = (
    "A continuación tienes el texto extraído de una credencial INE mexicana. "
    "Extrae SOLO los datos de la persona titular, ignorando leyendas, códigos y datos de otros. "
    "Devuelve un JSON con los siguientes campos: "
    f"{CAMPOS}. "
    "Si algún campo no está presente, dé<PERSON>lo vacío. "
    "Ejemplo de formato de salida:\n"
    "{\n"
    "  \"Nombre(s)\": \"EFREN\",\n"
    "  \"Apellido_Paterno\": \"CRUZ\",\n"
    "  \"Apellido_Materno\": \"AVILA\",\n"
    "  \"CURP\": \"CUAE860802HJCRVF07\",\n"
    "  \"Clave_Elector\": \"CRAV860802H\",\n"
    "  \"CIC\": \"1234567890\",\n"
    "  \"Domicilio\": \"CALLE FICTICIA 123\",\n"
    "  \"Colonia\": \"CENTRO\",\n"
    "  \"C.P.\": \"44100\",\n"
    "  \"Municipio\": \"GUADALAJARA\",\n"
    "  \"Estado\": \"JALISCO\"\n"
    "}\n"
    "Texto extraído:\n"
)

def extract_ine(texto_ocr, api_key):
    """
    Extrae los campos clave de una credencial INE mexicana usando un LLM (Mistral).
    Args:
        texto_ocr (str): Texto extraído por OCR de la INE.
        api_key (str): API Key de Mistral.
    Returns:
        dict: Diccionario con los campos extraídos.
    """
    client = Mistral(api_key=api_key)
    response = client.chat.complete(
        model="mistral-small-latest",
        messages=[{"role": "user", "content": PROMPT + texto_ocr}],
        temperature=0.1,
        max_tokens=512
    )
    content = response.choices[0].message.content
    print("\n--- RESPUESTA CRUDA DEL LLM (INE) ---\n", content, "\n-----------------------------\n")
    return extraer_json_de_llm(content) 