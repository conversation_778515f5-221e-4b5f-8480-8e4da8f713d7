
================================================================
        INSTRUCCIONES PARA DISTRIBUIR DESEMPAPELADOR RH
================================================================

COMO COMPARTIR CON USUARIOS:

1. Comparte el archivo: Desempapelador-RH-FACIL_v20250603.zip
   O comparte la carpeta: Desempapelador-RH-FACIL

2. Dile al usuario:
   "Extrae el ZIP y haz doble clic en ABRIR_DESEMPAPELADOR.bat"

3. ESO ES TODO. No necesitan saber nada más.

================================================================

QUE HACE EL ARCHIVO "ABRIR_DESEMPAPELADOR.bat":

✅ Detecta si Python está instalado
✅ Instala Python automáticamente si no está
✅ Instala todas las dependencias automáticamente  
✅ Abre la aplicación en el navegador
✅ Todo funciona con API Key incluida

================================================================

EXPERIENCIA DEL USUARIO:

1. Usuario extrae el ZIP
2. Usuario hace doble clic en "ABRIR_DESEMPAPELADOR.bat"
3. La primera vez: se instala todo automáticamente (5-10 minutos)
4. Siguientes veces: se abre inmediatamente
5. Usuario sube archivos y descarga resultados

================================================================

SOPORTE:

Si el usuario tiene problemas:
1. Que cierre todo y vuelva a hacer doble clic
2. Que ejecute como administrador (clic derecho)
3. Que verifique conexión a internet (solo primera vez)

================================================================

ARCHIVOS EN LA DISTRIBUCIÓN:

- ABRIR_DESEMPAPELADOR.bat  ← EL ÚNICO ARCHIVO QUE NECESITA EL USUARIO
- COMO_USAR.txt             ← Instrucciones súper simples
- app.py                    ← Aplicación principal (con API Key incluida)
- Otros archivos técnicos   ← El usuario no los toca

================================================================

¡La distribución está lista! 🚀
