#!/usr/bin/env python3
"""
Script de diagnóstico para verificar que la instalación local funcione correctamente.
"""

import os
import sys
import platform
import subprocess
import socket
from pathlib import Path


def print_header():
    """Imprime el encabezado del diagnóstico."""
    print("🔍 DIAGNÓSTICO DEL SISTEMA - DESEMPAPELADOR RH")
    print("=" * 60)
    print()


def check_system_info():
    """Verifica información del sistema."""
    print("🖥️  INFORMACIÓN DEL SISTEMA")
    print("-" * 30)
    
    system = platform.system()
    version = platform.version()
    architecture = platform.architecture()[0]
    
    print(f"Sistema Operativo: {system}")
    print(f"Versión: {version}")
    print(f"Arquitectura: {architecture}")
    
    # Detectar ChromeOS
    if system == "Linux":
        try:
            with open('/etc/lsb-release', 'r') as f:
                if 'chromeos' in f.read().lower():
                    print("🌐 ChromeOS detectado")
        except:
            pass
    
    print()


def check_python():
    """Verifica la instalación de Python."""
    print("🐍 VERIFICACIÓN DE PYTHON")
    print("-" * 30)
    
    try:
        version = sys.version
        executable = sys.executable
        print(f"✅ Python encontrado: {version}")
        print(f"Ejecutable: {executable}")
        
        # Verificar pip
        try:
            result = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                                  capture_output=True, text=True)
            print(f"✅ pip: {result.stdout.strip()}")
        except:
            print("❌ pip no encontrado")
            
    except Exception as e:
        print(f"❌ Error con Python: {e}")
    
    print()


def check_project_files():
    """Verifica que los archivos del proyecto estén presentes."""
    print("📁 VERIFICACIÓN DE ARCHIVOS DEL PROYECTO")
    print("-" * 30)
    
    required_files = [
        "app.py",
        "main.py", 
        "ocr.py",
        "extraction.py",
        "requirements.txt",
        "launcher.py"
    ]
    
    optional_files = [
        "Iniciar_Desempapelador.bat",
        "Desempapelador.cmd",
        "iniciar_chromeos.py",
        "Instalar_Dependencias.bat"
    ]
    
    print("Archivos requeridos:")
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} - FALTANTE")
    
    print("\nArchivos opcionales:")
    for file in optional_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ⚠️  {file} - No encontrado")
    
    print()


def check_dependencies():
    """Verifica las dependencias de Python."""
    print("📦 VERIFICACIÓN DE DEPENDENCIAS")
    print("-" * 30)
    
    dependencies = [
        ("streamlit", "Streamlit"),
        ("pandas", "Pandas"),
        ("mistralai", "Mistral AI"),
        ("pydantic", "Pydantic"),
        ("PIL", "Pillow"),
        ("fitz", "PyMuPDF"),
        ("dotenv", "python-dotenv"),
        ("openpyxl", "OpenPyXL")
    ]
    
    missing = []
    
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"  ✅ {name}")
        except ImportError:
            print(f"  ❌ {name} - NO INSTALADO")
            missing.append(name)
    
    if missing:
        print(f"\n⚠️  Dependencias faltantes: {', '.join(missing)}")
        print("💡 Ejecuta 'Instalar_Dependencias.bat' (Windows) o 'pip install -r requirements.txt'")
    else:
        print("\n✅ Todas las dependencias están instaladas")
    
    print()


def check_network():
    """Verifica la conectividad de red."""
    print("🌐 VERIFICACIÓN DE RED")
    print("-" * 30)
    
    try:
        # Verificar conectividad básica
        socket.create_connection(("*******", 53), timeout=3)
        print("✅ Conectividad a internet: OK")
        
        # Verificar acceso a PyPI
        try:
            import urllib.request
            urllib.request.urlopen("https://pypi.org", timeout=5)
            print("✅ Acceso a PyPI: OK")
        except:
            print("⚠️  Acceso a PyPI: Limitado")
            
    except:
        print("❌ Sin conectividad a internet")
        print("💡 Verifica tu conexión para instalar dependencias")
    
    print()


def check_ports():
    """Verifica que los puertos necesarios estén disponibles."""
    print("🔌 VERIFICACIÓN DE PUERTOS")
    print("-" * 30)
    
    ports_to_check = [8501, 8502, 8503]
    
    for port in ports_to_check:
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                print(f"  ✅ Puerto {port}: Disponible")
        except OSError:
            print(f"  ⚠️  Puerto {port}: En uso")
    
    print()


def check_permissions():
    """Verifica permisos de escritura."""
    print("🔐 VERIFICACIÓN DE PERMISOS")
    print("-" * 30)
    
    try:
        # Verificar escritura en directorio actual
        test_file = "test_permissions.tmp"
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print("✅ Permisos de escritura: OK")
        
        # Verificar directorio home para configuración
        home_dir = Path.home()
        streamlit_dir = home_dir / ".streamlit"
        print(f"✅ Directorio home accesible: {home_dir}")
        
    except Exception as e:
        print(f"❌ Error de permisos: {e}")
    
    print()


def run_quick_test():
    """Ejecuta una prueba rápida de importación."""
    print("🧪 PRUEBA RÁPIDA DE FUNCIONALIDAD")
    print("-" * 30)
    
    try:
        # Intentar importar módulos principales
        from ocr import ocr_mistral
        print("✅ Módulo OCR: OK")
        
        from extraction import extraer_campos_llm
        print("✅ Módulo extracción: OK")
        
        from utils.zip_handler import ZipHandler
        print("✅ Módulo ZIP: OK")
        
        from extractors.structured_ocr import structured_ocr
        print("✅ Módulo OCR estructurado: OK")
        
        print("\n✅ Todos los módulos principales funcionan correctamente")
        
    except Exception as e:
        print(f"❌ Error en prueba de funcionalidad: {e}")
    
    print()


def print_recommendations():
    """Imprime recomendaciones basadas en el diagnóstico."""
    print("💡 RECOMENDACIONES")
    print("-" * 30)
    
    system = platform.system()
    
    if system == "Windows":
        print("Para Windows:")
        print("  1. Ejecuta 'Instalar_Dependencias.bat' para instalar todo automáticamente")
        print("  2. Usa 'Desempapelador.cmd' para ejecutar la aplicación")
        print("  3. Si hay problemas, ejecuta como administrador")
    else:
        print("Para ChromeOS/Linux:")
        print("  1. Ejecuta 'python3 iniciar_chromeos.py'")
        print("  2. El script instalará dependencias automáticamente")
        print("  3. Asegúrate de que Linux esté activado en ChromeOS")
    
    print("\nGeneral:")
    print("  - Ten tu API Key de Mistral lista")
    print("  - Usa archivos ZIP para procesar múltiples documentos")
    print("  - No cierres la ventana de comandos mientras usas la app")
    
    print()


def main():
    """Función principal del diagnóstico."""
    print_header()
    
    check_system_info()
    check_python()
    check_project_files()
    check_dependencies()
    check_network()
    check_ports()
    check_permissions()
    run_quick_test()
    
    print("🏁 DIAGNÓSTICO COMPLETADO")
    print("=" * 60)
    
    print_recommendations()
    
    input("\nPresiona Enter para salir...")


if __name__ == "__main__":
    main()
