import os
import json
from mistralai import Mistral
from extractors.utils import extraer_json_de_llm

CAMPOS = [
    "Nombre(s)",
    "Apellido_Paterno",
    "Apellido_Materno",
    "<PERSON><PERSON>_de_Nacimiento",
    "CURP",
    "<PERSON>o",
    "<PERSON><PERSON>_de_Nacimiento",
    "Municipio_de_Registro",
    "Entidad_de_Registro"
]

PROMPT = (
    "A continuación tienes el texto extraído de un acta de nacimiento mexicana. "
    "Los datos de la persona registrada pueden estar en líneas separadas, a veces antes o después de la etiqueta. "
    "Busca el bloque que dice 'Datos de la Persona Registrada' y extrae SOLO los datos de la persona registrada, "
    "ignorando los de los padres, testigos o anotaciones marginales. "
    "Devuelve un JSON con los siguientes campos: "
    f"{CAMPOS}. "
    "Si algún campo no está presente, déjalo vacío. "
    "Ejemplo de formato de salida:\n"
    "{\n"
    "  \"Nombre(s)\": \"EFREN\",\n"
    "  \"Apellido_Paterno\": \"CRUZ\",\n"
    "  \"Apellido_Materno\": \"AVILA\",\n"
    "  \"Fecha_de_Nacimiento\": \"02/08/1986\",\n"
    "  \"CURP\": \"CUAE860802HJCRVF07\",\n"
    "  \"Sexo\": \"HOMBRE\",\n"
    "  \"Lugar_de_Nacimiento\": \"JALISCO\",\n"
    "  \"Municipio_de_Registro\": \"GUADALAJARA\",\n"
    "  \"Entidad_de_Registro\": \"JALISCO\"\n"
    "}\n"
    "Texto extraído:\n"
)

def extract_acta(texto_ocr, api_key):
    """
    Extrae los campos clave de un acta de nacimiento mexicana usando un LLM (Mistral).
    Args:
        texto_ocr (str): Texto extraído por OCR del acta.
        api_key (str): API Key de Mistral.
    Returns:
        dict: Diccionario con los campos extraídos.
    """
    client = Mistral(api_key=api_key)
    response = client.chat.complete(
        model="mistral-small-latest",
        messages=[{"role": "user", "content": PROMPT + texto_ocr}],
        temperature=0.1,
        max_tokens=512
    )
    content = response.choices[0].message.content
    print("\n--- RESPUESTA CRUDA DEL LLM (ACTA) ---\n", content, "\n-----------------------------\n")
    # Utiliza la utilidad para extraer el bloque JSON de la respuesta del LLM
    return extraer_json_de_llm(content) 