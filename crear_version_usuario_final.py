#!/usr/bin/env python3
"""
Script para crear una versión súper simple para usuarios finales no técnicos.
Crea una carpeta con solo lo esencial y un archivo para hacer doble clic.
"""

import os
import shutil
import zipfile
from pathlib import Path
from datetime import datetime


def crear_carpeta_usuario_final():
    """Crea la carpeta para usuarios finales con solo lo esencial."""
    
    # Nombre de la carpeta final
    carpeta_nombre = "Desempapelador-RH-FACIL"
    
    # Eliminar carpeta existente si existe
    if os.path.exists(carpeta_nombre):
        shutil.rmtree(carpeta_nombre)
    
    # Crear carpeta nueva
    os.makedirs(carpeta_nombre)
    
    print(f"📁 Creando carpeta para usuarios finales: {carpeta_nombre}")
    
    # Archivos esenciales que DEBEN estar
    archivos_esenciales = [
        "app.py",
        "main.py", 
        "ocr.py",
        "extraction.py",
        "requirements.txt",
        "launcher.py",
        "ABRIR_DESEMPAPELADOR.bat",  # El archivo principal
        "COMO_USAR.txt"              # Instrucciones súper simples
    ]
    
    # Directorios esenciales
    directorios_esenciales = [
        "extractors",
        "utils"
    ]
    
    # Copiar archivos esenciales
    print("📄 Copiando archivos esenciales...")
    for archivo in archivos_esenciales:
        if os.path.exists(archivo):
            shutil.copy2(archivo, carpeta_nombre)
            print(f"  ✅ {archivo}")
        else:
            print(f"  ⚠️  {archivo} - No encontrado")
    
    # Copiar directorios esenciales
    print("📁 Copiando directorios...")
    for directorio in directorios_esenciales:
        if os.path.exists(directorio):
            dest_dir = os.path.join(carpeta_nombre, directorio)
            shutil.copytree(directorio, dest_dir)
            print(f"  ✅ {directorio}/")
        else:
            print(f"  ⚠️  {directorio}/ - No encontrado")
    
    return carpeta_nombre


def limpiar_archivos_innecesarios(carpeta):
    """Elimina archivos que no necesita el usuario final."""
    
    print("🧹 Limpiando archivos innecesarios...")
    
    # Patrones de archivos a eliminar
    patrones_eliminar = [
        "**/__pycache__",
        "**/*.pyc",
        "**/*.pyo",
        "**/.DS_Store", 
        "**/Thumbs.db",
        "**/*.tmp"
    ]
    
    for patron in patrones_eliminar:
        for archivo in Path(carpeta).glob(patron):
            if archivo.is_file():
                archivo.unlink()
                print(f"  🗑️  {archivo.relative_to(carpeta)}")
            elif archivo.is_dir():
                shutil.rmtree(archivo)
                print(f"  🗑️  {archivo.relative_to(carpeta)}/")


def crear_zip_final(carpeta):
    """Crea un ZIP final listo para distribuir."""
    
    timestamp = datetime.now().strftime("%Y%m%d")
    zip_nombre = f"{carpeta}_v{timestamp}.zip"
    
    print(f"📦 Creando ZIP final: {zip_nombre}")
    
    with zipfile.ZipFile(zip_nombre, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(carpeta):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, os.path.dirname(carpeta))
                zipf.write(file_path, arc_name)
                print(f"  📄 {arc_name}")
    
    return zip_nombre


def crear_instrucciones_distribucion(carpeta, zip_archivo):
    """Crea instrucciones para el distribuidor."""
    
    instrucciones = f"""
================================================================
        INSTRUCCIONES PARA DISTRIBUIR DESEMPAPELADOR RH
================================================================

COMO COMPARTIR CON USUARIOS:

1. Comparte el archivo: {zip_archivo}
   O comparte la carpeta: {carpeta}

2. Dile al usuario:
   "Extrae el ZIP y haz doble clic en ABRIR_DESEMPAPELADOR.bat"

3. ESO ES TODO. No necesitan saber nada más.

================================================================

QUE HACE EL ARCHIVO "ABRIR_DESEMPAPELADOR.bat":

✅ Detecta si Python está instalado
✅ Instala Python automáticamente si no está
✅ Instala todas las dependencias automáticamente  
✅ Abre la aplicación en el navegador
✅ Todo funciona con API Key incluida

================================================================

EXPERIENCIA DEL USUARIO:

1. Usuario extrae el ZIP
2. Usuario hace doble clic en "ABRIR_DESEMPAPELADOR.bat"
3. La primera vez: se instala todo automáticamente (5-10 minutos)
4. Siguientes veces: se abre inmediatamente
5. Usuario sube archivos y descarga resultados

================================================================

SOPORTE:

Si el usuario tiene problemas:
1. Que cierre todo y vuelva a hacer doble clic
2. Que ejecute como administrador (clic derecho)
3. Que verifique conexión a internet (solo primera vez)

================================================================

ARCHIVOS EN LA DISTRIBUCIÓN:

- ABRIR_DESEMPAPELADOR.bat  ← EL ÚNICO ARCHIVO QUE NECESITA EL USUARIO
- COMO_USAR.txt             ← Instrucciones súper simples
- app.py                    ← Aplicación principal (con API Key incluida)
- Otros archivos técnicos   ← El usuario no los toca

================================================================

¡La distribución está lista! 🚀
"""
    
    with open("INSTRUCCIONES_DISTRIBUCION.txt", "w", encoding="utf-8") as f:
        f.write(instrucciones)
    
    print("📋 Creadas instrucciones para distribución: INSTRUCCIONES_DISTRIBUCION.txt")


def main():
    """Función principal."""
    
    print("🎯 CREADOR DE VERSIÓN PARA USUARIOS FINALES")
    print("=" * 60)
    print()
    
    # Verificar que estamos en el directorio correcto
    if not os.path.exists("app.py"):
        print("❌ Error: No se encuentra app.py")
        print("   Ejecuta este script desde la carpeta del proyecto")
        return
    
    try:
        # Crear carpeta para usuarios finales
        carpeta = crear_carpeta_usuario_final()
        
        # Limpiar archivos innecesarios
        limpiar_archivos_innecesarios(carpeta)
        
        # Crear ZIP final
        zip_archivo = crear_zip_final(carpeta)
        
        # Crear instrucciones para distribución
        crear_instrucciones_distribucion(carpeta, zip_archivo)
        
        # Mostrar resumen
        print("\n" + "=" * 60)
        print("✅ VERSIÓN PARA USUARIOS FINALES CREADA")
        print("=" * 60)
        
        print(f"\n📁 Carpeta: {carpeta}")
        print(f"📦 ZIP: {zip_archivo}")
        
        # Contar archivos
        total_archivos = sum([len(files) for r, d, files in os.walk(carpeta)])
        tamaño_zip = os.path.getsize(zip_archivo) / (1024*1024)  # MB
        
        print(f"\n📊 Estadísticas:")
        print(f"   - Total de archivos: {total_archivos}")
        print(f"   - Tamaño del ZIP: {tamaño_zip:.1f} MB")
        
        print(f"\n🎯 PARA DISTRIBUIR:")
        print(f"   1. Comparte: {zip_archivo}")
        print(f"   2. Dile al usuario: 'Extrae y haz doble clic en ABRIR_DESEMPAPELADOR.bat'")
        print(f"   3. ¡ESO ES TODO!")
        
        print(f"\n💡 EXPERIENCIA DEL USUARIO:")
        print(f"   1. Extrae el ZIP")
        print(f"   2. Doble clic en ABRIR_DESEMPAPELADOR.bat")
        print(f"   3. Espera (primera vez instala automáticamente)")
        print(f"   4. Usa la aplicación")
        
        print(f"\n📋 Lee: INSTRUCCIONES_DISTRIBUCION.txt para más detalles")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n" + "=" * 60)
    input("Presiona Enter para salir...")


if __name__ == "__main__":
    main()
