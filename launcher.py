#!/usr/bin/env python3
"""
Lanzador automático para la aplicación Desempapelador RH.
Este script inicia automáticamente la aplicación Streamlit sin necesidad de comandos.
"""

import os
import sys
import subprocess
import webbrowser
import time
import threading
import socket
from pathlib import Path


def find_free_port(start_port=8501):
    """Encuentra un puerto libre para Streamlit."""
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    return start_port


def check_dependencies():
    """Verifica que las dependencias estén instaladas."""
    try:
        import streamlit
        import pandas
        import mistralai
        import pydantic
        print("✅ Todas las dependencias están instaladas")
        return True
    except ImportError as e:
        print(f"❌ Falta instalar dependencias: {e}")
        print("\n🔧 Para instalar las dependencias, ejecuta:")
        print("pip install -r requirements.txt")
        return False


def open_browser_delayed(url, delay=3):
    """Abre el navegador después de un retraso."""
    def delayed_open():
        time.sleep(delay)
        print(f"🌐 Abriendo navegador en: {url}")
        webbrowser.open(url)
    
    thread = threading.Thread(target=delayed_open)
    thread.daemon = True
    thread.start()


def main():
    """Función principal del lanzador."""
    print("🚀 DESEMPAPELADOR RH - LANZADOR AUTOMÁTICO")
    print("=" * 50)
    
    # Verificar que estamos en el directorio correcto
    if not os.path.exists("app.py"):
        print("❌ Error: No se encuentra app.py")
        print("   Asegúrate de ejecutar este script desde la carpeta del proyecto")
        input("\nPresiona Enter para salir...")
        return
    
    # Verificar dependencias
    if not check_dependencies():
        input("\nPresiona Enter para salir...")
        return
    
    # Encontrar puerto libre
    port = find_free_port()
    url = f"http://localhost:{port}"
    
    print(f"🌐 Iniciando aplicación en: {url}")
    print("📱 La aplicación se abrirá automáticamente en tu navegador")
    print("\n⚠️  Para cerrar la aplicación, presiona Ctrl+C en esta ventana")
    print("=" * 50)
    
    # Programar apertura del navegador
    open_browser_delayed(url)
    
    try:
        # Iniciar Streamlit
        cmd = [
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", str(port),
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false",
            "--server.address", "localhost"
        ]
        
        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        print("\n\n👋 Aplicación cerrada por el usuario")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Error al iniciar Streamlit: {e}")
        print("\n🔧 Soluciones posibles:")
        print("1. Instala Streamlit: pip install streamlit")
        print("2. Verifica que Python esté correctamente instalado")
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
    
    input("\nPresiona Enter para salir...")


if __name__ == "__main__":
    main()
