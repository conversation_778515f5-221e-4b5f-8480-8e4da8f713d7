@echo off
title Desempapelador RH - Instalador de Dependencias
color 0B

echo.
echo  ================================================================
echo  ^|                INSTALADOR DE DEPENDENCIAS                   ^|
echo  ^|                   Desempapelador RH                          ^|
echo  ================================================================
echo.

REM Verificar que Python está instalado
echo [INFO] Verificando Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python no esta instalado
    echo.
    echo SOLUCION:
    echo 1. Descarga Python desde: https://python.org/downloads/
    echo 2. Durante la instalacion, marca "Add Python to PATH"
    echo 3. Reinicia este instalador
    echo.
    echo Abriendo pagina de descarga de Python...
    start https://python.org/downloads/
    pause
    exit /b 1
)

echo [OK] Python encontrado
python --version

REM Verificar que estamos en el directorio correcto
if not exist "requirements.txt" (
    echo [ERROR] No se encuentra requirements.txt
    echo         Asegurate de ejecutar este archivo desde la carpeta del proyecto
    pause
    exit /b 1
)

echo.
echo [INFO] Creando entorno virtual...
if not exist "venv" (
    python -m venv venv
    if errorlevel 1 (
        echo [ERROR] Error creando entorno virtual
        echo [INFO] Continuando con instalacion global...
    ) else (
        echo [OK] Entorno virtual creado
    )
) else (
    echo [OK] Entorno virtual ya existe
)

REM Activar entorno virtual si existe
if exist "venv\Scripts\activate.bat" (
    echo [INFO] Activando entorno virtual...
    call venv\Scripts\activate.bat
)

echo.
echo [INFO] Actualizando pip...
python -m pip install --upgrade pip

echo.
echo [INFO] Instalando dependencias...
echo        Esto puede tomar varios minutos...
echo.

REM Instalar dependencias una por una para mejor control
echo [INFO] Instalando Streamlit...
pip install streamlit
if errorlevel 1 (
    echo [AVISO] Error instalando Streamlit
)

echo [INFO] Instalando Pandas...
pip install pandas
if errorlevel 1 (
    echo [AVISO] Error instalando Pandas
)

echo [INFO] Instalando Mistral AI...
pip install "mistralai[document]"
if errorlevel 1 (
    echo [AVISO] Error instalando Mistral AI
)

echo [INFO] Instalando Pydantic...
pip install pydantic
if errorlevel 1 (
    echo [AVISO] Error instalando Pydantic
)

echo [INFO] Instalando dependencias adicionales...
pip install python-dotenv PyMuPDF Pillow openpyxl
if errorlevel 1 (
    echo [AVISO] Error instalando dependencias adicionales
)

echo.
echo [INFO] Verificando instalacion...
python -c "import streamlit, pandas, mistralai, pydantic; print('[OK] Todas las dependencias instaladas correctamente')"
if errorlevel 1 (
    echo [ERROR] Algunas dependencias no se instalaron correctamente
    echo.
    echo [INFO] Intentando instalacion completa desde requirements.txt...
    pip install -r requirements.txt
)

echo.
echo ================================================================
echo [OK] INSTALACION COMPLETADA
echo.
echo Para usar la aplicacion:
echo    1. Haz doble clic en "Desempapelador.cmd"
echo    2. O ejecuta "Iniciar_Desempapelador.bat"
echo.
echo La aplicacion se abrira automaticamente en tu navegador
echo ================================================================
echo.

pause
