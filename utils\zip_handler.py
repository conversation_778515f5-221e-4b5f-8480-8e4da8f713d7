import os
import zipfile
import tempfile
import shutil
from pathlib import Path
from typing import List, Tuple, Dict
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ZipHandler:
    """
    Utility class for handling ZIP file operations including extraction,
    file type detection, and temporary file management.
    """
    
    SUPPORTED_EXTENSIONS = {'.pdf', '.jpg', '.jpeg', '.png', '.zip'}
    
    def __init__(self):
        self.temp_dirs = []  # Track temporary directories for cleanup
    
    def is_supported_file(self, filename: str) -> bool:
        """
        Check if a file has a supported extension.
        
        Args:
            filename (str): Name of the file to check
            
        Returns:
            bool: True if file is supported, False otherwise
        """
        return Path(filename).suffix.lower() in self.SUPPORTED_EXTENSIONS
    
    def extract_zip(self, zip_path: str, extract_to: str = None) -> Tuple[str, List[str]]:
        """
        Extract a ZIP file and return the extraction directory and list of extracted files.
        
        Args:
            zip_path (str): Path to the ZIP file
            extract_to (str, optional): Directory to extract to. If None, creates temp directory.
            
        Returns:
            Tuple[str, List[str]]: (extraction_directory, list_of_extracted_files)
            
        Raises:
            zipfile.BadZipFile: If the ZIP file is corrupted
            FileNotFoundError: If the ZIP file doesn't exist
        """
        if not os.path.exists(zip_path):
            raise FileNotFoundError(f"ZIP file not found: {zip_path}")
        
        if extract_to is None:
            extract_to = tempfile.mkdtemp(prefix="zip_extract_")
            self.temp_dirs.append(extract_to)
        
        extracted_files = []
        
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # Get list of files in ZIP
                zip_files = zip_ref.namelist()
                logger.info(f"Found {len(zip_files)} files in ZIP: {zip_path}")
                
                for file_info in zip_ref.infolist():
                    # Skip directories
                    if file_info.is_dir():
                        continue
                    
                    # Extract file
                    extracted_path = zip_ref.extract(file_info, extract_to)
                    
                    # Only include supported files
                    if self.is_supported_file(file_info.filename):
                        extracted_files.append(extracted_path)
                        logger.info(f"Extracted supported file: {file_info.filename}")
                    else:
                        logger.warning(f"Skipped unsupported file: {file_info.filename}")
                
        except zipfile.BadZipFile as e:
            logger.error(f"Invalid ZIP file: {zip_path}")
            raise zipfile.BadZipFile(f"Invalid ZIP file: {zip_path}") from e
        
        return extract_to, extracted_files
    
    def process_zip_recursively(self, zip_path: str) -> List[str]:
        """
        Process a ZIP file recursively, extracting nested ZIP files as well.
        
        Args:
            zip_path (str): Path to the ZIP file
            
        Returns:
            List[str]: List of all extracted supported files
        """
        all_files = []
        extract_dir, extracted_files = self.extract_zip(zip_path)
        
        for file_path in extracted_files:
            if file_path.lower().endswith('.zip'):
                # Recursively process nested ZIP files
                nested_files = self.process_zip_recursively(file_path)
                all_files.extend(nested_files)
            else:
                all_files.append(file_path)
        
        return all_files
    
    def get_file_info(self, file_path: str) -> Dict[str, str]:
        """
        Get information about a file including its type and original name.
        
        Args:
            file_path (str): Path to the file
            
        Returns:
            Dict[str, str]: Dictionary with file information
        """
        path_obj = Path(file_path)
        return {
            'path': file_path,
            'name': path_obj.name,
            'extension': path_obj.suffix.lower(),
            'size': os.path.getsize(file_path) if os.path.exists(file_path) else 0,
            'type': self._detect_document_type(path_obj.name)
        }
    
    def _detect_document_type(self, filename: str) -> str:
        """
        Detect document type from filename.
        
        Args:
            filename (str): Name of the file
            
        Returns:
            str: Detected document type
        """
        filename_lower = filename.lower()
        
        if "acta" in filename_lower:
            return "acta"
        elif "nss" in filename_lower:
            return "nss"
        elif "aviso" in filename_lower:
            return "aviso_retencion"
        elif "comprobante" in filename_lower:
            return "comprobante_domicilio"
        elif "sat" in filename_lower:
            return "sat"
        elif "ine" in filename_lower:
            return "ine"
        elif "cuenta" in filename_lower:
            return "cuenta"
        else:
            return "otro"
    
    def cleanup(self):
        """
        Clean up all temporary directories created by this handler.
        """
        for temp_dir in self.temp_dirs:
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                    logger.info(f"Cleaned up temporary directory: {temp_dir}")
                except Exception as e:
                    logger.warning(f"Failed to clean up {temp_dir}: {e}")
        self.temp_dirs.clear()
    
    def __del__(self):
        """Cleanup when object is destroyed."""
        self.cleanup()


def extract_files_from_zip(zip_path: str) -> List[Dict[str, str]]:
    """
    Convenience function to extract files from a ZIP and return file information.
    
    Args:
        zip_path (str): Path to the ZIP file
        
    Returns:
        List[Dict[str, str]]: List of file information dictionaries
    """
    handler = ZipHandler()
    try:
        extracted_files = handler.process_zip_recursively(zip_path)
        return [handler.get_file_info(file_path) for file_path in extracted_files]
    except Exception as e:
        logger.error(f"Error processing ZIP file {zip_path}: {e}")
        return []
    finally:
        # Note: Don't cleanup here as files are still needed for processing
        pass
