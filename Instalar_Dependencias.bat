@echo off
chcp 65001 >nul
title Desempapelador RH - Instalador de Dependencias
color 0B

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                🔧 INSTALADOR DE DEPENDENCIAS                 ║
echo  ║                   Desempapelador RH                          ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

REM Verificar que Python está instalado
echo 🔍 Verificando Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Python no está instalado
    echo.
    echo 🔧 SOLUCIÓN:
    echo 1. Descarga Python desde: https://python.org/downloads/
    echo 2. Durante la instalación, marca "Add Python to PATH"
    echo 3. Reinicia este instalador
    echo.
    echo 💡 Abriendo página de descarga de Python...
    start https://python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python encontrado
python --version

REM Verificar que estamos en el directorio correcto
if not exist "requirements.txt" (
    echo ❌ Error: No se encuentra requirements.txt
    echo    Asegúrate de ejecutar este archivo desde la carpeta del proyecto
    pause
    exit /b 1
)

echo.
echo 🔧 Creando entorno virtual...
if not exist "venv" (
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Error creando entorno virtual
        echo 💡 Continuando con instalación global...
    ) else (
        echo ✅ Entorno virtual creado
    )
) else (
    echo ✅ Entorno virtual ya existe
)

REM Activar entorno virtual si existe
if exist "venv\Scripts\activate.bat" (
    echo 🔧 Activando entorno virtual...
    call venv\Scripts\activate.bat
)

echo.
echo 🔧 Actualizando pip...
python -m pip install --upgrade pip

echo.
echo 📦 Instalando dependencias...
echo    Esto puede tomar varios minutos...
echo.

REM Instalar dependencias una por una para mejor control
echo 🔄 Instalando Streamlit...
pip install streamlit
if errorlevel 1 (
    echo ⚠️ Error instalando Streamlit
)

echo 🔄 Instalando Pandas...
pip install pandas
if errorlevel 1 (
    echo ⚠️ Error instalando Pandas
)

echo 🔄 Instalando Mistral AI...
pip install "mistralai[document]"
if errorlevel 1 (
    echo ⚠️ Error instalando Mistral AI
)

echo 🔄 Instalando Pydantic...
pip install pydantic
if errorlevel 1 (
    echo ⚠️ Error instalando Pydantic
)

echo 🔄 Instalando dependencias adicionales...
pip install python-dotenv PyMuPDF Pillow openpyxl
if errorlevel 1 (
    echo ⚠️ Error instalando dependencias adicionales
)

echo.
echo 🔍 Verificando instalación...
python -c "import streamlit, pandas, mistralai, pydantic; print('✅ Todas las dependencias instaladas correctamente')"
if errorlevel 1 (
    echo ❌ Algunas dependencias no se instalaron correctamente
    echo.
    echo 🔧 Intentando instalación completa desde requirements.txt...
    pip install -r requirements.txt
)

echo.
echo ══════════════════════════════════════════════════════════════
echo ✅ INSTALACIÓN COMPLETADA
echo.
echo 🚀 Para usar la aplicación:
echo    1. Haz doble clic en "Desempapelador.cmd"
echo    2. O ejecuta "Iniciar_Desempapelador.bat"
echo.
echo 💡 La aplicación se abrirá automáticamente en tu navegador
echo ══════════════════════════════════════════════════════════════
echo.

pause
