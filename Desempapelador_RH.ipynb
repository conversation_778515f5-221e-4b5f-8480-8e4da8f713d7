# 🔧 SETUP - Instalación Automática de Componentes
print("🚀 DESEMPAPELADOR RH - Iniciando...")
print("⚙️ Instalando componentes necesarios (esto puede tomar 1-2 minutos)...")

# Instalar dependencias con versiones específicas
%pip install -q httpx>=0.28.1
%pip install -q mistralai python-dotenv PyMuPDF pandas Pillow openpyxl pydantic

# Importar módulos
import os
import json
import fitz
from mistralai import Mistral
from PIL import Image
import tempfile
import zipfile
import shutil
import pandas as pd
from pathlib import Path
from google.colab import files

print("🔑 Configurando API Key incluida...")

# API Key incluida
MISTRAL_API_KEY = "swUM1obVjv5AVlD5BhWImQMj49HXm3jX"

# Cliente Mistral global
mistral_client = Mistral(api_key=MISTRAL_API_KEY)

class ZipHandler:
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        self.extracted_files = []
    
    def cleanup(self):
        try:
            shutil.rmtree(self.temp_dir)
        except Exception as e:
            print(f"Error limpiando temporales: {e}")
    
    def process_zip_recursively(self, zip_path):
        supported_extensions = {".pdf", ".jpg", ".jpeg", ".png"}
        
        def extract_nested_zip(zip_path, extract_path):
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_path)
                
            for root, _, files in os.walk(extract_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    if file.lower().endswith(".zip"):
                        nested_extract_path = os.path.join(self.temp_dir, f"nested_{len(self.extracted_files)}")
                        os.makedirs(nested_extract_path, exist_ok=True)
                        extract_nested_zip(file_path, nested_extract_path)
                    elif any(file.lower().endswith(ext) for ext in supported_extensions):
                        self.extracted_files.append(file_path)
        
        extract_path = os.path.join(self.temp_dir, "root")
        os.makedirs(extract_path, exist_ok=True)
        extract_nested_zip(zip_path, extract_path)
        return self.extracted_files
    
    def get_file_info(self, file_path):
        name = os.path.basename(file_path)
        name_lower = name.lower()
        
        if "acta" in name_lower:
            tipo = "acta"
        elif "nss" in name_lower:
            tipo = "nss"
        elif "aviso" in name_lower:
            tipo = "aviso_retencion"
        elif "comprobante" in name_lower:
            tipo = "comprobante_domicilio"
        elif "sat" in name_lower:
            tipo = "sat"
        elif "ine" in name_lower:
            tipo = "ine"
        elif "cuenta" in name_lower:
            tipo = "cuenta"
        else:
            tipo = "otro"
            
        return {"name": name, "type": tipo, "path": file_path}

def convertir_imagen_a_pdf(path_imagen):
    with Image.open(path_imagen) as img:
        img = img.convert("RGB")
        temp_pdf = tempfile.NamedTemporaryFile(delete=False, suffix=".pdf")
        img.save(temp_pdf, format="PDF")
        temp_pdf.close()
        return temp_pdf.name

def extract_text_pymupdf(pdf_path):
    doc = fitz.open(pdf_path)
    extracted_text = "\n\n".join([page.get_text() for page in doc])
    return extracted_text.strip()

def ocr_mistral(document_path):
    archivo_a_procesar = document_path
    temp_pdf_path = None

    if document_path.lower().endswith((".jpg", ".jpeg", ".png")):
        temp_pdf_path = convertir_imagen_a_pdf(document_path)
        archivo_a_procesar = temp_pdf_path

    try:
        with open(archivo_a_procesar, "rb") as f:
            uploaded_pdf = mistral_client.files.upload(
                file={
                    "file_name": os.path.basename(archivo_a_procesar),
                    "content": f,
                },
                purpose="ocr"
            )
        signed_url = mistral_client.files.get_signed_url(file_id=uploaded_pdf.id)

        ocr_response = mistral_client.ocr.process(
            model="mistral-ocr-latest",
            document={
                "type": "document_url",
                "document_url": signed_url.url
            },
            include_image_base64=False
        )

        extracted_text = "\n\n".join(
            [page.markdown for page in ocr_response.pages if hasattr(page, "markdown") and page.markdown]
        )

        if not extracted_text.strip() or all('![' in page.markdown for page in ocr_response.pages if hasattr(page, "markdown")):
            print("⚠️ Usando PyMuPDF como fallback...")
            extracted_text = extract_text_pymupdf(archivo_a_procesar)

        return extracted_text

    except Exception as e:
        print(f"❌ Error en OCR: {e}")
        return ""

    finally:
        if temp_pdf_path and os.path.exists(temp_pdf_path):
            try:
                os.remove(temp_pdf_path)
            except Exception as e:
                print(f"⚠️ Error eliminando temporal: {e}")

def extract_fields(texto_ocr, tipo_documento):
    try:
        prompts = {
            "acta": "Extrae de esta acta de nacimiento en formato JSON: {Nombre(s), Apellido_Paterno, Apellido_Materno, Fecha_de_Nacimiento, CURP, Lugar_de_Nacimiento}",
            "nss": "Extrae de este documento del IMSS en formato JSON: {NSS, Nombre(s), Apellido_Paterno, Apellido_Materno, CURP}",
            "ine": "Extrae de esta credencial INE/IFE en formato JSON: {Nombre(s), Apellido_Paterno, Apellido_Materno, Domicilio, CURP, Clave_Elector}",
            "sat": "Extrae de este documento del SAT en formato JSON: {Nombre(s), Apellido_Paterno, Apellido_Materno, RFC, CURP}",
            "aviso_retencion": "Extrae de este aviso en formato JSON: {Nombre(s), Apellido_Paterno, Apellido_Materno, NSS, Numero_Credito_Infonavit, Tipo_Descuento, Factor_Descuento}",
            "comprobante_domicilio": "Extrae de este comprobante en formato JSON: {Domicilio, Colonia, C.P., Municipio, Estado}",
            "cuenta": "Extrae de este estado de cuenta en formato JSON: {Banco, Cuenta, CLABE, Titular}"
        }
        
        prompt = f"{prompts.get(tipo_documento, 'Extrae la información relevante en formato JSON')}\n\nTexto:\n{texto_ocr}"
        
        response = mistral_client.chat.complete(
            model="mistral-large-latest",
            messages=[
                {"role": "system", "content": "Extrae datos de documentos oficiales mexicanos en formato JSON. Sé preciso y conciso."},
                {"role": "user", "content": prompt}
            ]
        )

        response_text = response.choices[0].message.content
        start = response_text.find('{')
        end = response_text.rfind('}')
        if start >= 0 and end >= 0:
            json_str = response_text[start:end+1]
            return json.loads(json_str)
        return {}
    except Exception as e:
        print(f"❌ Error extrayendo campos: {str(e)}")
        return {}

print("✅ Setup completado - Ejecuta la siguiente celda para procesar documentos")

# === PROCESAR DOCUMENTOS === #

print("📤 Selecciona tus archivos (PDF, JPG, PNG o ZIP)...")
archivos_subidos = files.upload()
print(f"✅ {len(archivos_subidos)} archivos subidos\n")

resultados_planos = []
zip_handler = ZipHandler()

try:
    for nombre, contenido in archivos_subidos.items():
        print(f"\n📄 Procesando: {nombre}")
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=f".{nombre.split('.')[-1]}") as tmp:
            tmp.write(contenido)
            tmp_path = tmp.name

        try:
            if nombre.lower().endswith(".zip"):
                print(f"📦 Extrayendo archivos del ZIP...")
                archivos_extraidos = zip_handler.process_zip_recursively(tmp_path)
                
                total = len(archivos_extraidos)
                for i, archivo in enumerate(archivos_extraidos, 1):
                    info = zip_handler.get_file_info(archivo)
                    print(f"  📑 Procesando archivo {i}/{total}: {info['name']}")
                    
                    try:
                        texto = ocr_mistral(archivo)
                        if texto:
                            print(f"    ✅ OCR completado: {len(texto)} caracteres")
                            campos = extract_fields(texto, info['type'])
                            if campos:
                                print(f"    ✅ Campos extraídos")
                                # Aplanar el resultado
                                campos_planos = {
                                    'Archivo': info['name'],
                                    'Tipo': info['type'],
                                    **campos
                                }
                                resultados_planos.append(campos_planos)
                    except Exception as e:
                        print(f"    ❌ Error: {str(e)}")
                        continue
            else:
                nombre_lower = nombre.lower()
                tipo = next((t for t, p in {
                    "acta": "acta",
                    "nss": "nss",
                    "aviso": "aviso_retencion",
                    "comprobante": "comprobante_domicilio",
                    "sat": "sat",
                    "ine": "ine",
                    "cuenta": "cuenta"
                }.items() if t in nombre_lower), "otro")

                texto = ocr_mistral(tmp_path)
                if texto:
                    print(f"✅ OCR completado: {len(texto)} caracteres")
                    campos = extract_fields(texto, tipo)
                    if campos:
                        print("✅ Campos extraídos")
                        # Aplanar el resultado
                        campos_planos = {
                            'Archivo': nombre,
                            'Tipo': tipo,
                            **campos
                        }
                        resultados_planos.append(campos_planos)

        except Exception as e:
            print(f"❌ Error procesando {nombre}: {str(e)}")

        finally:
            if os.path.exists(tmp_path):
                os.remove(tmp_path)

finally:
    zip_handler.cleanup()

if resultados_planos:
    print("\n✨ Generando resultados...")
    df = pd.DataFrame(resultados_planos)
    # Reordenar columnas
    cols = ['Archivo', 'Tipo'] + [col for col in df.columns if col not in ['Archivo', 'Tipo']]
    df = df[cols]
    display(df)
    
    print("\n📥 Descargando Excel...")
    excel_path = 'expedientes_consolidados.xlsx'
    df.to_excel(excel_path, index=False)
    files.download(excel_path)
    print("\n✅ ¡Proceso completado! El Excel se descargará automáticamente.")
else:
    print("❌ No se obtuvieron resultados")