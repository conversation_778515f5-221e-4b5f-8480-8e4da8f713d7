import os
import json
import fitz  # PyMuPDF
from mistralai import Mistral, DocumentURLChunk, ImageURLChunk
from dotenv import load_dotenv
from PIL import Image
import tempfile
import zipfile
import base64
from pathlib import Path
from typing import List, Dict, Union
from utils.zip_handler import <PERSON>ipHandler, extract_files_from_zip
from extractors.structured_ocr import structured_ocr, StructuredOCR
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def cargar_api_key():
    """
    Carga la API Key de Mistral desde las variables de entorno.
    Lanza un error si no está definida.
    Returns:
        str: API Key de Mistral
    """
    load_dotenv()
    api_key = os.environ.get("MISTRAL_API_KEY")
    if not api_key:
        raise ValueError("Error: La variable de entorno MISTRAL_API_KEY no está definida.")
    return api_key

def convertir_imagen_a_pdf(path_imagen):
    """
    Convierte una imagen (JPG, PNG) a PDF temporal para procesar con OCR.
    Args:
        path_imagen (str): Ruta de la imagen.
    Returns:
        str: Ruta del PDF temporal generado.
    """
    with Image.open(path_imagen) as img:
        img = img.convert("RGB")
        temp_pdf = tempfile.NamedTemporaryFile(delete=False, suffix=".pdf")
        img.save(temp_pdf, format="PDF")
        temp_pdf.close()
        return temp_pdf.name

def extract_text_pymupdf(pdf_path):
    """
    Extrae texto de un PDF usando PyMuPDF (fallback si OCR falla).
    Args:
        pdf_path (str): Ruta del PDF.
    Returns:
        str: Texto extraído.
    """
    doc = fitz.open(pdf_path)
    extracted_text = "\n\n".join([page.get_text() for page in doc])
    return extracted_text.strip()

def ocr_mistral(document_path, api_key=None):
    """
    Realiza OCR sobre un documento usando Mistral OCR. Si falla o no hay texto útil, usa PyMuPDF como fallback.
    Args:
        document_path (str): Ruta del archivo PDF o imagen.
        api_key (str, opcional): API Key de Mistral. Si no se pasa, se carga del entorno.
    Returns:
        str: Texto extraído del documento.
    """
    if api_key is None:
        api_key = cargar_api_key()
    client = Mistral(api_key=api_key)
    archivo_a_procesar = document_path
    temp_pdf_path = None
    # Si es imagen, convertir a PDF
    if document_path.lower().endswith((".jpg", ".jpeg", ".png")):
        temp_pdf_path = convertir_imagen_a_pdf(document_path)
        archivo_a_procesar = temp_pdf_path
    try:
        # Subir el archivo y obtener la URL firmada
        with open(archivo_a_procesar, "rb") as f:
            uploaded_pdf = client.files.upload(
                file={
                    "file_name": os.path.basename(archivo_a_procesar),
                    "content": f,
                },
                purpose="ocr"
            )
        signed_url = client.files.get_signed_url(file_id=uploaded_pdf.id)
        # Procesar OCR
        ocr_response = client.ocr.process(
            model="mistral-ocr-latest",
            document={
                "type": "document_url",
                "document_url": signed_url.url
            },
            include_image_base64=False
        )
        # Extraer texto de las páginas
        extracted_text = "\n\n".join(
            [page.markdown for page in ocr_response.pages if hasattr(page, "markdown") and page.markdown]
        )
        # Fallback: si el texto está vacío o solo contiene imágenes, usar PyMuPDF
        if not extracted_text.strip() or all('![' in page.markdown for page in ocr_response.pages if hasattr(page, "markdown")):
            print("⚠️ Mistral no devolvió texto útil, usando PyMuPDF como fallback...")
            extracted_text = extract_text_pymupdf(archivo_a_procesar)
        return extracted_text
    except Exception as e:
        print(f"❌ Error durante el procesamiento OCR: {e}")
        return ""
    finally:
        # Eliminar el PDF temporal si se creó y ya está cerrado
        if temp_pdf_path and os.path.exists(temp_pdf_path):
            try:
                os.remove(temp_pdf_path)
            except Exception as e:
                print(f"⚠️ No se pudo eliminar el archivo temporal: {e}")

def is_zip_file(file_path: str) -> bool:
    """
    Check if a file is a ZIP archive.

    Args:
        file_path (str): Path to the file

    Returns:
        bool: True if file is a ZIP archive, False otherwise
    """
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_ref:
            return True
    except (zipfile.BadZipFile, FileNotFoundError):
        return False


def process_zip_file(zip_path: str, api_key: str = None, use_structured: bool = False) -> List[Dict]:
    """
    Process a ZIP file containing documents, extracting and processing each supported file.

    Args:
        zip_path (str): Path to the ZIP file
        api_key (str, optional): API Key de Mistral. Si no se pasa, se carga del entorno.
        use_structured (bool): Whether to use structured OCR processing

    Returns:
        List[Dict]: List of processing results for each file in the ZIP
    """
    if api_key is None:
        api_key = cargar_api_key()

    zip_handler = ZipHandler()
    results = []

    try:
        # Extract files from ZIP
        extracted_files = zip_handler.process_zip_recursively(zip_path)
        logger.info(f"Extracted {len(extracted_files)} files from ZIP: {zip_path}")

        for file_path in extracted_files:
            file_info = zip_handler.get_file_info(file_path)
            logger.info(f"Processing file: {file_info['name']}")

            try:
                if use_structured:
                    # Use structured OCR
                    structured_result = structured_ocr(file_path, api_key)
                    results.append({
                        'file_name': file_info['name'],
                        'file_path': file_path,
                        'document_type': file_info['type'],
                        'structured_data': structured_result,
                        'success': True,
                        'error': None
                    })
                else:
                    # Use traditional OCR
                    text_content = ocr_mistral(file_path, api_key)
                    results.append({
                        'file_name': file_info['name'],
                        'file_path': file_path,
                        'document_type': file_info['type'],
                        'text_content': text_content,
                        'success': True,
                        'error': None
                    })

            except Exception as e:
                logger.error(f"Error processing file {file_info['name']}: {e}")
                results.append({
                    'file_name': file_info['name'],
                    'file_path': file_path,
                    'document_type': file_info['type'],
                    'text_content': '',
                    'success': False,
                    'error': str(e)
                })

    except Exception as e:
        logger.error(f"Error processing ZIP file {zip_path}: {e}")
        results.append({
            'file_name': Path(zip_path).name,
            'file_path': zip_path,
            'document_type': 'zip_error',
            'text_content': '',
            'success': False,
            'error': str(e)
        })

    finally:
        # Cleanup will happen when zip_handler is destroyed
        pass

    return results


def ocr_mistral_enhanced(document_path: str, api_key: str = None, use_structured: bool = False) -> Union[str, List[Dict], StructuredOCR]:
    """
    Enhanced OCR function that can handle individual files or ZIP archives.

    Args:
        document_path (str): Path to the document or ZIP file
        api_key (str, optional): API Key de Mistral. Si no se pasa, se carga del entorno.
        use_structured (bool): Whether to use structured OCR processing

    Returns:
        Union[str, List[Dict], StructuredOCR]:
            - For single files: text content (str) or StructuredOCR object
            - For ZIP files: List of processing results
    """
    if api_key is None:
        api_key = cargar_api_key()

    # Check if it's a ZIP file
    if is_zip_file(document_path):
        logger.info(f"Processing ZIP file: {document_path}")
        return process_zip_file(document_path, api_key, use_structured)
    else:
        # Process single file
        if use_structured:
            logger.info(f"Processing single file with structured OCR: {document_path}")
            return structured_ocr(document_path, api_key)
        else:
            logger.info(f"Processing single file with traditional OCR: {document_path}")
            return ocr_mistral(document_path, api_key)


# Este archivo está listo para importar las funciones OCR en otros módulos.
# Funciones disponibles:
# - ocr_mistral: OCR tradicional para archivos individuales
# - ocr_mistral_enhanced: OCR mejorado que soporta ZIP y OCR estructurado
# - process_zip_file: Procesamiento específico de archivos ZIP
# - structured_ocr: OCR estructurado usando Pydantic (importado desde extractors.structured_ocr)

