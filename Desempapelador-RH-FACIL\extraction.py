import os
import time
import pandas as pd
from extractors import (
    extract_acta,
    extract_nss,
    extract_aviso_retencion,
    extract_comprobante_domicilio,
    extract_sat,
    extract_ine,
    extract_cuenta
)

"""
Módulo de orquestación de extracción semántica.
- Llama a los extractores específicos por tipo de documento.
- Implementa lógica de reintentos y manejo de rate limit.
- Ensambla y vincula los resultados por colaborador usando identificadores robustos (CURP, RFC, NSS, nombre, domicilio).
"""

def extraer_identificador(campos):
    """
    Determina el identificador principal del colaborador a partir de los campos extraídos.
    Prioridad: CURP > RFC > Nombre completo > NSS
    Args:
        campos (dict): Diccionario de campos extraídos.
    Returns:
        str: Identificador único (CURP, RFC, nombre o NSS)
    """
    if "CURP" in campos and campos["CURP"]:
        return campos["CURP"]
    if "RFC" in campos and campos["RFC"]:
        return campos["RFC"]
    if all(k in campos for k in ["Nombre(s)", "Apellido_Paterno", "Apellido_Materno"]):
        nombre = f"{campos['Nombre(s)']} {campos['Apellido_Paterno']} {campos['Apellido_Materno']}".strip()
        if nombre:
            return nombre
    if "NSS" in campos and campos["NSS"]:
        return campos["NSS"]
    return ""

def llamar_extractor_con_reintentos(extractor, texto_ocr, api_key, max_reintentos=3, espera=10):
    """
    Llama a un extractor con reintentos automáticos en caso de rate limit.
    Args:
        extractor (func): Función extractor.
        texto_ocr (str): Texto extraído por OCR.
        api_key (str): API Key de Mistral.
        max_reintentos (int): Número máximo de reintentos.
        espera (int): Segundos de espera entre reintentos.
    Returns:
        dict: Campos extraídos.
    """
    for intento in range(max_reintentos):
        try:
            return extractor(texto_ocr, api_key)
        except Exception as e:
            if "429" in str(e) or "rate limit" in str(e).lower():
                print(f"⚠️ Rate limit alcanzado. Esperando {espera} segundos antes de reintentar (intento {intento+1}/{max_reintentos})...")
                time.sleep(espera)
            else:
                print(f"❌ Error inesperado en extractor: {e}")
                break
    print("❌ No se pudo procesar el documento tras varios intentos.")
    return {}

def extraer_campos_llm(texto_ocr, tipo_documento, api_key=None):
    """
    Llama al extractor adecuado según el tipo de documento y retorna los campos extraídos y el identificador.
    Args:
        texto_ocr (str): Texto extraído por OCR.
        tipo_documento (str): Tipo de documento detectado.
        api_key (str, opcional): API Key de Mistral. Si no se pasa, se carga del entorno.
    Returns:
        dict: {"id_colaborador": str, "campos": dict}
    """
    if api_key is None:
        api_key = os.environ.get("MISTRAL_API_KEY")
    if tipo_documento == "acta":
        campos = llamar_extractor_con_reintentos(extract_acta, texto_ocr, api_key)
    elif tipo_documento == "nss":
        campos = llamar_extractor_con_reintentos(extract_nss, texto_ocr, api_key)
    elif tipo_documento == "aviso_retencion":
        campos = llamar_extractor_con_reintentos(extract_aviso_retencion, texto_ocr, api_key)
    elif tipo_documento == "comprobante_domicilio":
        campos = llamar_extractor_con_reintentos(extract_comprobante_domicilio, texto_ocr, api_key)
    elif tipo_documento == "sat":
        campos = llamar_extractor_con_reintentos(extract_sat, texto_ocr, api_key)
    elif tipo_documento == "ine":
        campos = llamar_extractor_con_reintentos(extract_ine, texto_ocr, api_key)
    elif tipo_documento == "cuenta":
        campos = llamar_extractor_con_reintentos(extract_cuenta, texto_ocr, api_key)
    else:
        campos = {}
    id_colaborador = extraer_identificador(campos)
    return {"id_colaborador": id_colaborador, "campos": campos}

# Ensamblador robusto: vincula documentos por CURP, RFC, NSS, nombre completo y domicilio
# Forza el match de comprobante de domicilio y cuenta bancaria por nombre o domicilio si no hay CURP

def ensamblar_por_colaborador(resultados):
    """
    Agrupa y vincula los resultados de extracción por colaborador usando identificadores robustos.
    Da prioridad a CURP, luego RFC, NSS, nombre y domicilio. Intenta vincular comprobante de domicilio y cuenta bancaria por nombre o domicilio si no hay identificador fuerte.
    Args:
        resultados (list): Lista de dicts con resultados de extracción.
    Returns:
        dict: Expedientes agrupados por colaborador.
    """
    # Índices auxiliares para vinculación secundaria
    por_curp = {}
    por_rfc = {}
    por_nss = {}
    por_nombre = {}
    por_domicilio = {}

    expedientes = {}
    pendientes = []  # Para documentos sin identificador fuerte

    for res in resultados:
        campos = res["campos"]
        tipo = res["tipo"]
        archivo = res["archivo"]
        curp = campos.get("CURP", "").strip().upper()
        rfc = campos.get("RFC", "").strip().upper()
        nss = campos.get("NSS", "").strip()
        nombre = (
            (campos.get("Nombre(s)", "") + " " +
             campos.get("Apellido_Paterno", "") + " " +
             campos.get("Apellido_Materno", "")).strip().upper()
        )
        domicilio = campos.get("Domicilio", "").strip().upper()

        # 1. Vinculación primaria por CURP
        id_col = None
        if curp:
            id_col = curp
        elif rfc:
            id_col = rfc
        elif nss:
            id_col = nss
        elif nombre and tipo != "comprobante_domicilio" and tipo != "cuenta":
            id_col = nombre
        else:
            # Para comprobante de domicilio y cuenta, intentar vincular después
            pendientes.append({"tipo": tipo, "archivo": archivo, "campos": campos, "nombre": nombre, "domicilio": domicilio})
            continue

        # Crea expediente si no existe
        if id_col not in expedientes:
            expedientes[id_col] = {}

        expedientes[id_col][tipo] = {
            "archivo": archivo,
            **campos
        }

        # Actualiza índices auxiliares
        if curp:
            por_curp[curp] = id_col
        if rfc:
            por_rfc[rfc] = id_col
        if nss:
            por_nss[nss] = id_col
        if nombre:
            por_nombre[nombre] = id_col
        if domicilio:
            por_domicilio[domicilio] = id_col

    # Intentar vincular comprobante de domicilio y cuenta bancaria por nombre o domicilio
    for doc in pendientes:
        tipo = doc["tipo"]
        archivo = doc["archivo"]
        campos = doc["campos"]
        nombre = doc["nombre"]
        domicilio = doc["domicilio"]
        id_col = None

        # 1. Buscar por nombre completo
        if nombre and nombre in por_nombre:
            id_col = por_nombre[nombre]
        # 2. Buscar por domicilio
        elif domicilio and domicilio in por_domicilio:
            id_col = por_domicilio[domicilio]
        # 3. Buscar por CURP en campos (por si acaso)
        elif campos.get("CURP", "").strip().upper() in por_curp:
            id_col = por_curp[campos.get("CURP", "").strip().upper()]
        # 4. Si no hay match, dejar como no_vinculado
        else:
            id_col = "no_vinculado"

        if id_col not in expedientes:
            expedientes[id_col] = {}
        expedientes[id_col][tipo] = {
            "archivo": archivo,
            **campos
        }

    return expedientes

def consolidar_empleados(expedientes, LAYOUT_CAMPOS=None, PRIORIDAD_CAMPOS=None, CAMPOS_DOMICILIO=None):
    """
    Consolida los datos de los expedientes agrupados en un DataFrame ancho, priorizando los campos según el tipo de documento.
    Realiza fusión de filas por nombre si hay duplicados sin CURP.
    Args:
        expedientes (dict): Expedientes agrupados por colaborador.
        LAYOUT_CAMPOS (list): Lista de campos objetivo.
        PRIORIDAD_CAMPOS (dict): Prioridad de tipos de documento por campo.
        CAMPOS_DOMICILIO (list): Lista de campos de domicilio.
    Returns:
        pd.DataFrame: DataFrame consolidado de empleados.
    """
    # Valores por defecto si no se pasan (para compatibilidad con main.py y app.py)
    if LAYOUT_CAMPOS is None:
        LAYOUT_CAMPOS = [
            "id_colaborador", "Apellido_Paterno", "Apellido_Materno", "Nombre(s)", "Fecha_de_Nacimiento", "CURP", "RFC", "NSS",
            "Domicilio", "Colonia", "C.P.", "Municipio", "Estado", "Numero_Credito_Infonavit", "Tipo_Descuento", "Factor_Descuento",
            "Banco", "Cuenta", "Cuenta_Clabe"
        ]
    if PRIORIDAD_CAMPOS is None:
        PRIORIDAD_CAMPOS = {
            "Domicilio": ["comprobante_domicilio", "sat", "ine", "acta", "nss", "aviso_retencion"],
            "Colonia": ["comprobante_domicilio", "sat", "ine", "acta", "nss", "aviso_retencion"],
            "C.P.": ["comprobante_domicilio", "sat", "ine", "acta", "nss", "aviso_retencion"],
            "Municipio": ["comprobante_domicilio", "sat", "ine", "acta", "nss", "aviso_retencion"],
            "Estado": ["comprobante_domicilio", "sat", "ine", "acta", "nss", "aviso_retencion"],
        }
    if CAMPOS_DOMICILIO is None:
        CAMPOS_DOMICILIO = ["Domicilio", "Colonia", "C.P.", "Municipio", "Estado"]

    empleados = []
    for id_col, docs in expedientes.items():
        fila = {campo: "" for campo in LAYOUT_CAMPOS}
        fila["id_colaborador"] = id_col
        for campo in LAYOUT_CAMPOS:
            prioridad = PRIORIDAD_CAMPOS.get(campo, docs.keys())
            for tipo in prioridad:
                if tipo in docs and campo in docs[tipo] and docs[tipo][campo]:
                    fila[campo] = docs[tipo][campo]
                    break  # Ya llené este campo con la mejor fuente
        empleados.append(fila)
    df = pd.DataFrame(empleados)

    # --- FUSIÓN POR NOMBRE EXACTO ---
    # Si una fila tiene CURP y otra solo nombre, y el nombre completo coincide, fusiona los datos de domicilio
    def nombre_completo(row):
        return f"{row['Nombre(s)']} {row['Apellido_Paterno']} {row['Apellido_Materno']}".strip().upper()

    # Índices para buscar coincidencias
    df_con_curp = df[df["CURP"] != ""].copy()
    df_sin_curp = df[(df["CURP"] == "") & (df["Nombre(s)"] != "")].copy()
    usados = set()
    for idx_sin, row_sin in df_sin_curp.iterrows():
        nombre_sin = nombre_completo(row_sin)
        for idx_con, row_con in df_con_curp.iterrows():
            nombre_con = nombre_completo(row_con)
            if nombre_sin == nombre_con:
                # Fusiona los campos de domicilio
                for campo in CAMPOS_DOMICILIO:
                    if row_sin[campo]:
                        df.at[idx_con, campo] = row_sin[campo]
                usados.add(idx_sin)
                break
    # Elimina las filas fusionadas (solo nombre)
    df = df.drop(list(usados)).reset_index(drop=True)
    return df 