@echo off
title Desempapelador RH
color 0A

REM Este es el UNICO archivo que el usuario necesita hacer doble clic
REM Instala todo automaticamente y abre la aplicacion

echo.
echo ================================================================
echo                    DESEMPAPELADOR RH
echo              Iniciando automaticamente...
echo ================================================================
echo.

REM Verificar que Python está instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo [INFO] Python no esta instalado. Instalando automaticamente...
    echo.
    echo Por favor espera mientras se descarga e instala Python...
    echo Esto puede tomar unos minutos...
    
    REM Descargar e instalar Python automaticamente
    powershell -Command "& {Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.0/python-3.11.0-amd64.exe' -OutFile 'python_installer.exe'}"
    
    if exist "python_installer.exe" (
        echo [INFO] Instalando Python...
        python_installer.exe /quiet InstallAllUsers=1 PrependPath=1
        
        REM Esperar a que termine la instalacion
        timeout /t 30 /nobreak >nul
        
        REM Limpiar instalador
        del python_installer.exe
        
        echo [OK] Python instalado correctamente
    ) else (
        echo [ERROR] No se pudo descargar Python
        echo.
        echo Por favor:
        echo 1. Ve a https://python.org/downloads/
        echo 2. Descarga e instala Python
        echo 3. Marca "Add Python to PATH" durante la instalacion
        echo 4. Vuelve a hacer doble clic en este archivo
        pause
        exit /b 1
    )
)

echo [OK] Python encontrado

REM Verificar que estamos en el directorio correcto
if not exist "app.py" (
    echo [ERROR] Archivos de la aplicacion no encontrados
    echo         Asegurate de que este archivo este en la carpeta correcta
    pause
    exit /b 1
)

REM Instalar dependencias automaticamente (sin preguntar)
echo [INFO] Verificando e instalando dependencias...
echo        Esto puede tomar unos minutos la primera vez...

REM Crear entorno virtual si no existe
if not exist "venv" (
    echo [INFO] Creando entorno virtual...
    python -m venv venv >nul 2>&1
)

REM Activar entorno virtual
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
)

REM Actualizar pip silenciosamente
python -m pip install --upgrade pip >nul 2>&1

REM Instalar dependencias silenciosamente
echo [INFO] Instalando componentes necesarios...
pip install streamlit pandas "mistralai[document]" pydantic python-dotenv PyMuPDF Pillow openpyxl >nul 2>&1

if errorlevel 1 (
    echo [INFO] Instalacion rapida fallo, intentando instalacion completa...
    pip install -r requirements.txt >nul 2>&1
)

echo [OK] Componentes instalados

REM Verificar instalacion
python -c "import streamlit" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] La instalacion no se completo correctamente
    echo.
    echo Soluciones:
    echo 1. Ejecuta este archivo como administrador (clic derecho → Ejecutar como administrador)
    echo 2. Verifica tu conexion a internet
    echo 3. Contacta soporte tecnico
    pause
    exit /b 1
)

echo [OK] Todo listo

echo.
echo [INFO] Abriendo Desempapelador RH...
echo [INFO] La aplicacion se abrira en tu navegador automaticamente
echo.
echo IMPORTANTE: NO cierres esta ventana mientras uses la aplicacion
echo Para cerrar la aplicacion, cierra esta ventana
echo.

REM Iniciar la aplicacion
python launcher.py

echo.
echo [INFO] Aplicacion cerrada
pause
