#!/usr/bin/env python3
"""
Lanzador automático para la aplicación Desempapelador RH.
Este script inicia automáticamente la aplicación Streamlit sin necesidad de comandos.
Optimizado para Windows y ChromeOS/Google OS.
"""

import os
import sys
import subprocess
import webbrowser
import time
import threading
import socket
import platform
from pathlib import Path


def is_windows():
    """Detecta si estamos en Windows."""
    return platform.system().lower() == 'windows'


def is_chromeos():
    """Detecta si estamos en ChromeOS."""
    try:
        with open('/etc/lsb-release', 'r') as f:
            content = f.read()
            return 'chromeos' in content.lower()
    except:
        return False


def find_free_port(start_port=8501):
    """Encuentra un puerto libre para Streamlit."""
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    return start_port


def check_dependencies():
    """Verifica que las dependencias estén instaladas."""
    missing_deps = []

    try:
        import streamlit
    except ImportError:
        missing_deps.append("streamlit")

    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas")

    try:
        import mistralai
    except ImportError:
        missing_deps.append("mistralai[document]")

    try:
        import pydantic
    except ImportError:
        missing_deps.append("pydantic")

    if missing_deps:
        print(f"❌ Faltan dependencias: {', '.join(missing_deps)}")
        return False

    print("✅ Todas las dependencias están instaladas")
    return True


def install_dependencies():
    """Instala las dependencias automáticamente."""
    print("🔧 Instalando dependencias...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"],
                      check=True, capture_output=True)
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                      check=True, capture_output=True)
        print("✅ Dependencias instaladas correctamente")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando dependencias: {e}")
        return False


def open_browser_delayed(url, delay=3):
    """Abre el navegador después de un retraso."""
    def delayed_open():
        time.sleep(delay)
        print(f"🌐 Abriendo navegador en: {url}")
        try:
            if is_windows():
                # En Windows, usar el navegador predeterminado
                os.startfile(url)
            else:
                # En otros sistemas (incluyendo ChromeOS)
                webbrowser.open(url)
        except Exception as e:
            print(f"⚠️ No se pudo abrir el navegador automáticamente: {e}")
            print(f"   Abre manualmente: {url}")

    thread = threading.Thread(target=delayed_open)
    thread.daemon = True
    thread.start()


def create_streamlit_config():
    """Crea un archivo de configuración temporal para Streamlit."""
    config_dir = Path.home() / ".streamlit"
    config_dir.mkdir(exist_ok=True)

    config_file = config_dir / "config.toml"
    config_content = """
[server]
headless = true
port = 8501
address = "localhost"

[browser]
gatherUsageStats = false
serverAddress = "localhost"
serverPort = 8501

[theme]
primaryColor = "#4F8BF9"
backgroundColor = "#FFFFFF"
secondaryBackgroundColor = "#F0F2F6"
textColor = "#262730"
"""

    with open(config_file, "w", encoding="utf-8") as f:
        f.write(config_content)

    return config_file


def main():
    """Función principal del lanzador."""
    # Detectar sistema operativo
    system = platform.system()
    print(f"🖥️  Sistema detectado: {system}")

    if is_chromeos():
        print("🌐 ChromeOS/Google OS detectado")
    elif is_windows():
        print("🪟 Windows detectado")

    print("\n🚀 DESEMPAPELADOR RH - LANZADOR AUTOMÁTICO")
    print("=" * 55)

    # Verificar que estamos en el directorio correcto
    if not os.path.exists("app.py"):
        print("❌ Error: No se encuentra app.py")
        print("   Asegúrate de ejecutar este script desde la carpeta del proyecto")
        print("\n📁 Archivos esperados:")
        print("   - app.py")
        print("   - requirements.txt")
        print("   - ocr.py")
        input("\nPresiona Enter para salir...")
        return

    # Verificar dependencias
    print("🔍 Verificando dependencias...")
    if not check_dependencies():
        print("\n🔧 ¿Quieres instalar las dependencias automáticamente? (s/n)")
        response = input().strip().lower()
        if response in ['s', 'si', 'sí', 'y', 'yes']:
            if not install_dependencies():
                input("\nPresiona Enter para salir...")
                return
            # Verificar nuevamente
            if not check_dependencies():
                print("❌ Aún faltan dependencias después de la instalación")
                input("\nPresiona Enter para salir...")
                return
        else:
            print("❌ No se pueden continuar sin las dependencias")
            input("\nPresiona Enter para salir...")
            return

    # Crear configuración de Streamlit
    config_file = create_streamlit_config()

    # Encontrar puerto libre
    port = find_free_port()
    url = f"http://localhost:{port}"

    print(f"\n🌐 Iniciando aplicación en: {url}")
    print("📱 La aplicación se abrirá automáticamente en tu navegador")
    print("\n⚠️  IMPORTANTE:")
    print("   - NO cierres esta ventana mientras uses la aplicación")
    print("   - Para cerrar la aplicación, presiona Ctrl+C aquí")
    print("   - Tu API Key de Mistral se ingresa en la aplicación web")
    print("=" * 55)

    # Programar apertura del navegador
    open_browser_delayed(url, delay=4)

    try:
        # Iniciar Streamlit con configuración optimizada
        cmd = [
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", str(port),
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false",
            "--server.address", "localhost",
            "--server.enableCORS", "false",
            "--server.enableXsrfProtection", "false"
        ]

        print("🔄 Iniciando servidor Streamlit...")
        subprocess.run(cmd, check=True)

    except KeyboardInterrupt:
        print("\n\n👋 Aplicación cerrada por el usuario")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Error al iniciar Streamlit: {e}")
        print("\n🔧 Soluciones posibles:")
        print("1. Verifica que Streamlit esté instalado: pip install streamlit")
        print("2. Verifica que Python esté correctamente instalado")
        print("3. Ejecuta como administrador si estás en Windows")
        print("4. Verifica que el puerto 8501 no esté en uso")
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")

    # Limpiar archivo de configuración
    try:
        if config_file.exists():
            config_file.unlink()
    except:
        pass

    print("\n" + "=" * 55)
    print("✅ Sesión terminada")
    if is_windows():
        input("\nPresiona Enter para salir...")


if __name__ == "__main__":
    main()
