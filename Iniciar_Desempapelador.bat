@echo off
chcp 65001 >nul
title Desempapelador RH - Lanzador Automático
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                🚀 DESEMPAPELADOR RH                          ║
echo  ║                   Lanzador Automático                        ║
echo  ║                                                              ║
echo  ║  Procesamiento automático de expedientes laborales          ║
echo  ║  con OCR y soporte para archivos ZIP                        ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

REM Verificar que estamos en el directorio correcto
if not exist "app.py" (
    echo ❌ Error: No se encuentra app.py
    echo    Asegúrate de que este archivo .bat esté en la carpeta del proyecto
    echo.
    echo 📁 Archivos esperados en esta carpeta:
    echo    - app.py
    echo    - requirements.txt
    echo    - ocr.py
    echo.
    pause
    exit /b 1
)

REM Verificar que Python está instalado
echo 🔍 Verificando Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Python no está instalado o no está en el PATH
    echo.
    echo 🔧 SOLUCIÓN:
    echo 1. Descarga Python desde: https://python.org/downloads/
    echo 2. Durante la instalación, marca "Add Python to PATH"
    echo 3. Reinicia esta aplicación
    echo.
    echo 💡 Tip: En Windows 10/11 también puedes instalar desde Microsoft Store
    echo.
    start https://python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python encontrado
python --version

REM Verificar si existe el entorno virtual
if exist "venv\Scripts\activate.bat" (
    echo 🔧 Activando entorno virtual...
    call venv\Scripts\activate.bat
    echo ✅ Entorno virtual activado
) else (
    echo ⚠️  Usando Python global (recomendamos crear un entorno virtual)
)

REM Verificar e instalar dependencias
echo.
echo 🔍 Verificando dependencias...
python -c "import streamlit, pandas, mistralai, pydantic" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Algunas dependencias faltan
    echo 🔧 Instalando dependencias automáticamente...
    echo    Esto puede tomar unos minutos...
    echo.

    pip install --upgrade pip
    pip install -r requirements.txt

    if errorlevel 1 (
        echo.
        echo ❌ Error al instalar dependencias
        echo.
        echo 🔧 SOLUCIONES POSIBLES:
        echo 1. Verifica tu conexión a internet
        echo 2. Ejecuta como administrador
        echo 3. Instala manualmente: pip install streamlit pandas mistralai pydantic
        echo.
        pause
        exit /b 1
    )
    echo ✅ Dependencias instaladas correctamente
) else (
    echo ✅ Todas las dependencias están instaladas
)

echo.
echo 🌐 Iniciando Desempapelador RH...
echo 📱 La aplicación se abrirá automáticamente en tu navegador
echo 🔗 URL: http://localhost:8501
echo.
echo ⚠️  IMPORTANTE:
echo    - NO cierres esta ventana mientras uses la aplicación
echo    - Para cerrar la aplicación, presiona Ctrl+C aquí
echo    - Tu API Key de Mistral se ingresa en la aplicación web
echo.
echo ══════════════════════════════════════════════════════════════
echo.

REM Crear un archivo de configuración temporal
echo [server] > .streamlit_config.toml
echo headless = true >> .streamlit_config.toml
echo port = 8501 >> .streamlit_config.toml
echo address = "localhost" >> .streamlit_config.toml
echo [browser] >> .streamlit_config.toml
echo gatherUsageStats = false >> .streamlit_config.toml

REM Iniciar la aplicación con configuración personalizada
python launcher.py

REM Limpiar archivo temporal
if exist ".streamlit_config.toml" del ".streamlit_config.toml"

echo.
echo 👋 Aplicación cerrada
pause
