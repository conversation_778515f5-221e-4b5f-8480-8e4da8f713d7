@echo off
title Desempapelador RH - Lanzador Automatico
color 0A

echo.
echo  ================================================================
echo  ^|                   DESEMPAPELADOR RH                          ^|
echo  ^|                  Lanzador Automatico                         ^|
echo  ^|                                                              ^|
echo  ^|  Procesamiento automatico de expedientes laborales          ^|
echo  ^|  con OCR y soporte para archivos ZIP                        ^|
echo  ================================================================
echo.

REM Verificar que estamos en el directorio correcto
if not exist "app.py" (
    echo [ERROR] No se encuentra app.py
    echo         Asegurate de que este archivo .bat este en la carpeta del proyecto
    echo.
    echo Archivos esperados en esta carpeta:
    echo    - app.py
    echo    - requirements.txt
    echo    - ocr.py
    echo.
    pause
    exit /b 1
)

REM Verificar que Python está instalado
echo [INFO] Verificando Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python no esta instalado o no esta en el PATH
    echo.
    echo SOLUCION:
    echo 1. Descarga Python desde: https://python.org/downloads/
    echo 2. Durante la instalacion, marca "Add Python to PATH"
    echo 3. Reinicia esta aplicacion
    echo.
    echo Tip: En Windows 10/11 tambien puedes instalar desde Microsoft Store
    echo.
    start https://python.org/downloads/
    pause
    exit /b 1
)

echo [OK] Python encontrado
python --version

REM Verificar si existe el entorno virtual
if exist "venv\Scripts\activate.bat" (
    echo [INFO] Activando entorno virtual...
    call venv\Scripts\activate.bat
    echo [OK] Entorno virtual activado
) else (
    echo [AVISO] Usando Python global (recomendamos crear un entorno virtual)
)

REM Verificar e instalar dependencias
echo.
echo [INFO] Verificando dependencias...
python -c "import streamlit, pandas, mistralai, pydantic" >nul 2>&1
if errorlevel 1 (
    echo [AVISO] Algunas dependencias faltan
    echo [INFO] Instalando dependencias automaticamente...
    echo         Esto puede tomar unos minutos...
    echo.

    pip install --upgrade pip
    pip install -r requirements.txt

    if errorlevel 1 (
        echo.
        echo [ERROR] Error al instalar dependencias
        echo.
        echo SOLUCIONES POSIBLES:
        echo 1. Verifica tu conexion a internet
        echo 2. Ejecuta como administrador
        echo 3. Instala manualmente: pip install streamlit pandas mistralai pydantic
        echo.
        pause
        exit /b 1
    )
    echo [OK] Dependencias instaladas correctamente
) else (
    echo [OK] Todas las dependencias estan instaladas
)

echo.
echo [INFO] Iniciando Desempapelador RH...
echo [INFO] La aplicacion se abrira automaticamente en tu navegador
echo [INFO] URL: http://localhost:8501
echo.
echo IMPORTANTE:
echo    - NO cierres esta ventana mientras uses la aplicacion
echo    - Para cerrar la aplicacion, presiona Ctrl+C aqui
echo    - Tu API Key de Mistral se ingresa en la aplicacion web
echo.
echo ================================================================
echo.

REM Crear un archivo de configuración temporal
echo [server] > .streamlit_config.toml
echo headless = true >> .streamlit_config.toml
echo port = 8501 >> .streamlit_config.toml
echo address = "localhost" >> .streamlit_config.toml
echo [browser] >> .streamlit_config.toml
echo gatherUsageStats = false >> .streamlit_config.toml

REM Iniciar la aplicación con configuración personalizada
python launcher.py

REM Limpiar archivo temporal
if exist ".streamlit_config.toml" del ".streamlit_config.toml"

echo.
echo [INFO] Aplicacion cerrada
pause
