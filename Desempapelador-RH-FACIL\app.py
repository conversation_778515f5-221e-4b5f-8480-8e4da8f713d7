import streamlit as st
import pandas as pd
import tempfile
import os
from pathlib import Path
from ocr import ocr_mistral, ocr_mistral_enhanced, is_zip_file
from extraction import extraer_campos_llm, ensamblar_por_colaborador, consolidar_empleados
from utils.zip_handler import ZipHandler

st.set_page_config(page_title="Desempapelador - Expediente RH", layout="wide")

# --- SIDEBAR: Branding, copy, instrucciones, API Key ---
with st.sidebar:
    st.image("https://cdn-icons-png.flaticon.com/512/2991/2991108.png", width=80)
    st.title("🧾 Desempapelador")
    st.markdown("""
    Convierte papeles en datos útiles. Sin llorar.

    Sube los documentos del expediente laboral de un empleado (PDF o imagen) y esta app los desmenuza con IA + OCR.
    Te entrega todo en una tabla editable, lista para exportar a Excel.

    ---
    ### 🔒 Privacidad
    - Tus archivos no se guardan.
    - Solo tú y Mixtral los ven.
    - Los datos se eliminan tras procesar.

    ---
    """)
    # Aviso de privacidad en expander (compatible con todas las versiones)
    with st.expander("🔒 Aviso de Privacidad"):
        st.markdown("""
        ### Aviso de Privacidad

        En **Desempapelador RH** la privacidad y seguridad de tu información es una prioridad.

        - **Tus archivos solo existen durante tu sesión.**  
          Los documentos que subes se procesan de forma temporal y se eliminan automáticamente después de usarse. No se guardan en ningún servidor ni base de datos.

        - **Nadie más puede ver tus archivos ni tus resultados.**  
          Cada usuario tiene su propia sesión aislada. Otros usuarios no pueden acceder a tus documentos ni a tus datos, aunque estén usando la app al mismo tiempo.

        - **Tu API Key de Mistral es privada.**  
          La clave que ingresas solo se utiliza en tu sesión y nunca se almacena ni se comparte.

        - **El código fuente está en un repositorio privado.**  
          Solo el responsable de la app tiene acceso al código y a la configuración.

        - **Solo el proveedor de la nube (Streamlit Cloud) podría, en teoría, acceder a los archivos temporales mientras existen.**  
          Esto es una limitación técnica de cualquier servicio en la nube, pero se confía en las buenas prácticas de privacidad del proveedor.

        **Si tienes dudas sobre la privacidad de tus datos, puedes contactarme directamente:**  
        📧 <EMAIL>
        """)
    # API Key con valor por defecto incluido
    api_key_default = "swUM1obVjv5AVlD5BhWImQMj49HXm3jX"
    api_key_usuario = st.text_input(
        "API Key de Mistral",
        value=api_key_default,
        type="password",
        help="API Key incluida por defecto. Puedes cambiarla si tienes tu propia clave."
    )

    # Mostrar información sobre la API Key
    if api_key_usuario == api_key_default:
        st.success("✅ Usando API Key incluida - ¡Listo para usar!")
        st.info("💡 No necesitas buscar una API Key, ya está incluida para tu comodidad.")
    else:
        st.success("🔑 Usando tu API Key personalizada")
        st.info("¿Quieres volver a la API Key incluida? Borra el campo y recarga la página.")

# --- MAIN: Zona central de acción ---
st.markdown("<h1 style='text-align: center;'>Desempapelador RH</h1>", unsafe_allow_html=True)
st.markdown("<h3 style='text-align: center; color: #4F8BF9;'>Automatiza la captura de expedientes laborales</h3>", unsafe_allow_html=True)
st.write("")

# Recomendación sobre el nombre de los archivos
st.info("""
**Recomendación:** Para facilitar la identificación automática, nombra tus archivos así:

- Usa un **ID de empleado corto y único** al inicio del nombre del archivo (ejemplo: 12345, abc01, etc.)
- Luego, agrega el tipo de documento (acta, nss, curp, ine, etc.)
- Ejemplo: `12345_acta.pdf`, `12345_nss.pdf`, `abc01_ine.jpg`

Esto ayuda a agrupar y vincular los documentos correctamente.
""")

# Zona de carga de archivos, centrada
st.markdown("### Sube tus archivos PDF, imagen o ZIP")
archivos_subidos = st.file_uploader(
    "Arrastra aquí uno o más archivos (PDF, JPG, PNG, ZIP)",
    type=["pdf", "jpg", "jpeg", "png", "zip"],
    accept_multiple_files=True,
    label_visibility="collapsed"
)

# Add information about ZIP support
if archivos_subidos:
    zip_files = [f for f in archivos_subidos if f.name.lower().endswith('.zip')]
    if zip_files:
        st.info(f"""
        📦 **Archivos ZIP detectados:** {len(zip_files)}

        Los archivos ZIP se extraerán automáticamente y se procesarán todos los documentos compatibles que contengan.
        Formatos soportados dentro del ZIP: PDF, JPG, PNG, y ZIP anidados.
        """)

# Add option for structured OCR
use_structured_ocr = st.checkbox(
    "🔬 Usar OCR Estructurado (Experimental)",
    value=False,
    help="Utiliza el nuevo sistema de OCR estructurado basado en Pydantic para mejor extracción de datos. Recomendado para documentos complejos."
)

# Botón de procesar, centrado y grande
col1, col2, col3 = st.columns([2,2,2])
with col2:
    procesar = st.button("🚀 Procesar documentos", use_container_width=True, disabled=not (archivos_subidos and api_key_usuario))

# Procesamiento y resultados
if archivos_subidos and procesar and api_key_usuario:
    resultados = []
    zip_handler = ZipHandler()

    with st.spinner("Procesando documentos..."):
        try:
            for archivo in archivos_subidos:
                # Save uploaded file to temporary location
                with tempfile.NamedTemporaryFile(delete=False, suffix="." + archivo.name.split(".")[-1]) as tmp:
                    tmp.write(archivo.read())
                    tmp_path = tmp.name

                # Check if it's a ZIP file
                if is_zip_file(tmp_path):
                    st.info(f"📦 Procesando archivo ZIP: {archivo.name}")

                    # Process ZIP file
                    zip_results = ocr_mistral_enhanced(tmp_path, api_key=api_key_usuario, use_structured=use_structured_ocr)

                    # Convert ZIP results to the expected format
                    for zip_result in zip_results:
                        if zip_result['success']:
                            if use_structured_ocr and 'structured_data' in zip_result:
                                # Handle structured OCR results
                                structured_data = zip_result['structured_data']
                                # Convert structured data to text for compatibility
                                texto = str(structured_data.ocr_contents)
                            else:
                                texto = zip_result.get('text_content', '')

                            tipo = zip_result['document_type']
                            resultado = extraer_campos_llm(texto, tipo, api_key=api_key_usuario)
                            resultados.append({
                                "archivo": zip_result['file_name'],
                                "tipo": tipo,
                                "id_colaborador": resultado.get("id_colaborador", ""),
                                "campos": resultado.get("campos", {})
                            })
                        else:
                            st.warning(f"⚠️ Error procesando {zip_result['file_name']}: {zip_result['error']}")
                else:
                    # Process individual file
                    if use_structured_ocr:
                        structured_result = ocr_mistral_enhanced(tmp_path, api_key=api_key_usuario, use_structured=True)
                        texto = str(structured_result.ocr_contents)
                    else:
                        texto = ocr_mistral(tmp_path, api_key=api_key_usuario)

                    # Detect document type
                    nombre = archivo.name.lower()
                    if "acta" in nombre:
                        tipo = "acta"
                    elif "nss" in nombre:
                        tipo = "nss"
                    elif "aviso" in nombre:
                        tipo = "aviso_retencion"
                    elif "comprobante" in nombre:
                        tipo = "comprobante_domicilio"
                    elif "sat" in nombre:
                        tipo = "sat"
                    elif "ine" in nombre:
                        tipo = "ine"
                    elif "cuenta" in nombre:
                        tipo = "cuenta"
                    else:
                        tipo = "otro"

                    resultado = extraer_campos_llm(texto, tipo, api_key=api_key_usuario)
                    resultados.append({
                        "archivo": archivo.name,
                        "tipo": tipo,
                        "id_colaborador": resultado.get("id_colaborador", ""),
                        "campos": resultado.get("campos", {})
                    })

                # Clean up temporary file
                os.remove(tmp_path)

        except Exception as e:
            st.error(f"❌ Error durante el procesamiento: {str(e)}")
        finally:
            # Clean up ZIP handler resources
            zip_handler.cleanup()
    expedientes = ensamblar_por_colaborador(resultados)
    df_empleados = consolidar_empleados(expedientes)
    st.success("¡Procesamiento completado!")
    st.markdown("### Resultados extraídos (puedes revisar y corregir antes de descargar):")
    df_edit = st.data_editor(df_empleados, num_rows="dynamic", use_container_width=True, key="edicion")
    # Descargar Excel
    excel_tmp = tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx")
    df_edit.to_excel(excel_tmp.name, index=False)
    excel_tmp.close()
    with open(excel_tmp.name, "rb") as f:
        st.download_button(
            label="⬇️ Descargar Excel consolidado",
            data=f,
            file_name="expedientes_consolidados.xlsx",
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
    os.remove(excel_tmp.name)
elif procesar and not api_key_usuario:
    st.error("Debes ingresar tu API Key de Mistral para procesar los documentos.")

# Pie de página
st.markdown("---")
st.markdown("<div style='text-align: center; color: gray;'>Hecho con ❤️ por Efrén · ¿Te ahorró tiempo? Invítame una cerveza en <a href='https://ko-fi.com/nominante' target='_blank'>ko-fi.com/nominante</a></div>", unsafe_allow_html=True) 