#!/usr/bin/env python3
"""
Lanzador específico para ChromeOS/Google OS.
Optimizado para el entorno Linux de ChromeOS.
"""

import os
import sys
import subprocess
import webbrowser
import time
import threading
import socket
from pathlib import Path


def check_chromeos_environment():
    """Verifica si estamos en ChromeOS y configura el entorno."""
    print("🌐 Configurando para ChromeOS/Google OS...")
    
    # Verificar si estamos en el contenedor Linux de ChromeOS
    if os.path.exists('/etc/apt/sources.list'):
        print("✅ Entorno Linux de ChromeOS detectado")
        return True
    
    print("⚠️ No se detectó ChromeOS, continuando con configuración estándar")
    return False


def install_python_chromeos():
    """Instala Python y pip en ChromeOS si no están disponibles."""
    print("🔧 Verificando Python en ChromeOS...")
    
    # Verificar si Python está instalado
    try:
        result = subprocess.run(['python3', '--version'], capture_output=True, text=True)
        print(f"✅ Python encontrado: {result.stdout.strip()}")
        return True
    except FileNotFoundError:
        print("❌ Python no encontrado")
        
        print("🔧 Instalando Python...")
        try:
            # Actualizar repositorios
            subprocess.run(['sudo', 'apt', 'update'], check=True)
            # Instalar Python y pip
            subprocess.run(['sudo', 'apt', 'install', '-y', 'python3', 'python3-pip'], check=True)
            print("✅ Python instalado correctamente")
            return True
        except subprocess.CalledProcessError:
            print("❌ Error instalando Python")
            return False


def find_free_port(start_port=8501):
    """Encuentra un puerto libre para Streamlit."""
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    return start_port


def check_dependencies():
    """Verifica que las dependencias estén instaladas."""
    missing_deps = []
    
    deps_to_check = ['streamlit', 'pandas', 'mistralai', 'pydantic']
    
    for dep in deps_to_check:
        try:
            __import__(dep)
        except ImportError:
            missing_deps.append(dep)
    
    if missing_deps:
        print(f"❌ Faltan dependencias: {', '.join(missing_deps)}")
        return False
    
    print("✅ Todas las dependencias están instaladas")
    return True


def install_dependencies_chromeos():
    """Instala las dependencias en ChromeOS."""
    print("🔧 Instalando dependencias para ChromeOS...")
    
    try:
        # Actualizar pip
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                      check=True)
        
        # Instalar dependencias del sistema que pueden ser necesarias
        print("📦 Instalando dependencias del sistema...")
        subprocess.run(['sudo', 'apt', 'install', '-y', 'python3-dev', 'build-essential'], 
                      check=True)
        
        # Instalar dependencias de Python
        print("🐍 Instalando dependencias de Python...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True)
        
        print("✅ Dependencias instaladas correctamente")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando dependencias: {e}")
        return False


def open_browser_chromeos(url, delay=3):
    """Abre el navegador en ChromeOS."""
    def delayed_open():
        time.sleep(delay)
        print(f"🌐 Abriendo navegador en: {url}")
        try:
            # En ChromeOS, intentar diferentes métodos para abrir el navegador
            try:
                # Método 1: usar xdg-open
                subprocess.run(['xdg-open', url], check=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                try:
                    # Método 2: usar google-chrome directamente
                    subprocess.run(['google-chrome', url], check=True)
                except (subprocess.CalledProcessError, FileNotFoundError):
                    # Método 3: usar webbrowser de Python
                    webbrowser.open(url)
        except Exception as e:
            print(f"⚠️ No se pudo abrir el navegador automáticamente: {e}")
            print(f"   Abre manualmente en Chrome: {url}")
    
    thread = threading.Thread(target=delayed_open)
    thread.daemon = True
    thread.start()


def main():
    """Función principal para ChromeOS."""
    print("🌐 DESEMPAPELADOR RH - LANZADOR PARA CHROMEOS")
    print("=" * 55)
    
    # Verificar entorno ChromeOS
    is_chromeos = check_chromeos_environment()
    
    # Verificar que estamos en el directorio correcto
    if not os.path.exists("app.py"):
        print("❌ Error: No se encuentra app.py")
        print("   Asegúrate de ejecutar este script desde la carpeta del proyecto")
        input("\nPresiona Enter para salir...")
        return
    
    # Verificar/instalar Python si es necesario
    if is_chromeos:
        if not install_python_chromeos():
            input("\nPresiona Enter para salir...")
            return
    
    # Verificar dependencias
    print("🔍 Verificando dependencias...")
    if not check_dependencies():
        print("\n🔧 ¿Quieres instalar las dependencias automáticamente? (s/n)")
        response = input().strip().lower()
        if response in ['s', 'si', 'sí', 'y', 'yes']:
            if is_chromeos:
                success = install_dependencies_chromeos()
            else:
                success = install_dependencies_chromeos()  # Usar el mismo método
            
            if not success:
                input("\nPresiona Enter para salir...")
                return
                
            # Verificar nuevamente
            if not check_dependencies():
                print("❌ Aún faltan dependencias después de la instalación")
                input("\nPresiona Enter para salir...")
                return
        else:
            print("❌ No se puede continuar sin las dependencias")
            input("\nPresiona Enter para salir...")
            return
    
    # Encontrar puerto libre
    port = find_free_port()
    url = f"http://localhost:{port}"
    
    print(f"\n🌐 Iniciando aplicación en: {url}")
    print("📱 La aplicación se abrirá automáticamente en Chrome")
    print("\n⚠️  IMPORTANTE:")
    print("   - NO cierres esta terminal mientras uses la aplicación")
    print("   - Para cerrar la aplicación, presiona Ctrl+C aquí")
    print("   - Tu API Key de Mistral se ingresa en la aplicación web")
    print("=" * 55)
    
    # Programar apertura del navegador
    open_browser_chromeos(url, delay=4)
    
    try:
        # Iniciar Streamlit
        cmd = [
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port", str(port),
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false",
            "--server.address", "localhost"
        ]
        
        print("🔄 Iniciando servidor Streamlit...")
        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        print("\n\n👋 Aplicación cerrada por el usuario")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Error al iniciar Streamlit: {e}")
        print("\n🔧 Soluciones posibles:")
        print("1. Verifica que Streamlit esté instalado")
        print("2. Verifica que Python esté correctamente instalado")
        print("3. Verifica que el puerto 8501 no esté en uso")
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
    
    print("\n" + "=" * 55)
    print("✅ Sesión terminada")
    input("\nPresiona Enter para salir...")


if __name__ == "__main__":
    main()
