@echo off
chcp 65001 >nul
title Desempapelador RH - Lanzador

echo.
echo 🚀 DESEMPAPELADOR RH - LANZADOR AUTOMÁTICO
echo ==================================================
echo.

REM Verificar que Python está instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Python no está instalado o no está en el PATH
    echo.
    echo 🔧 Para solucionar:
    echo 1. Instala Python desde https://python.org
    echo 2. Asegúrate de marcar "Add Python to PATH" durante la instalación
    echo.
    pause
    exit /b 1
)

REM Verificar que estamos en el directorio correcto
if not exist "app.py" (
    echo ❌ Error: No se encuentra app.py
    echo    Asegúrate de que este archivo .bat esté en la carpeta del proyecto
    echo.
    pause
    exit /b 1
)

REM Verificar si existe el entorno virtual
if exist "venv\Scripts\activate.bat" (
    echo 🔧 Activando entorno virtual...
    call venv\Scripts\activate.bat
) else (
    echo ⚠️  No se encontró entorno virtual, usando Python global
)

REM Verificar dependencias básicas
echo 🔍 Verificando dependencias...
python -c "import streamlit" >nul 2>&1
if errorlevel 1 (
    echo ❌ Streamlit no está instalado
    echo 🔧 Instalando dependencias...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ Error al instalar dependencias
        pause
        exit /b 1
    )
)

echo ✅ Dependencias verificadas
echo.
echo 🌐 Iniciando aplicación...
echo 📱 La aplicación se abrirá automáticamente en tu navegador
echo.
echo ⚠️  Para cerrar la aplicación, cierra esta ventana o presiona Ctrl+C
echo ==================================================
echo.

REM Iniciar la aplicación
python launcher.py

pause
