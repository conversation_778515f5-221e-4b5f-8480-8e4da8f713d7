#!/usr/bin/env python3
"""
Script de prueba para demostrar la funcionalidad de procesamiento de archivos ZIP
con OCR estructurado basado en el cookbook de Mistral.
"""

import os
import sys
from pathlib import Path
from ocr import ocr_mistral_enhanced, is_zip_file
from utils.zip_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, extract_files_from_zip
from extractors.structured_ocr import structured_ocr, batch_structured_ocr


def test_zip_detection():
    """Test ZIP file detection functionality."""
    print("=== Prueba de Detección de Archivos ZIP ===")
    
    # Test with existing files
    test_files = [
        "ActadeNacimiento.pdf",
        "NSS.pdf", 
        "Cuenta.jpg"
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            is_zip = is_zip_file(file_path)
            print(f"📄 {file_path}: {'ZIP' if is_zip else 'No ZIP'}")
        else:
            print(f"⚠️ {file_path}: Archivo no encontrado")
    
    print()


def test_structured_ocr():
    """Test structured OCR functionality."""
    print("=== Prueba de OCR Estructurado ===")
    
    # Get API key
    api_key = input("Introduce tu API Key de Mistral para la prueba: ").strip()
    if not api_key:
        print("❌ Se necesita una API Key para probar el OCR estructurado.")
        return
    
    # Test with an existing file
    test_file = "ActadeNacimiento.pdf"
    if os.path.exists(test_file):
        print(f"🔬 Procesando {test_file} con OCR estructurado...")
        try:
            result = structured_ocr(test_file, api_key)
            print(f"✅ Resultado exitoso:")
            print(f"   - Archivo: {result.file_name}")
            print(f"   - Temas: {result.topics}")
            print(f"   - Idioma: {result.languages}")
            print(f"   - Tipo de documento: {result.document_type}")
            print(f"   - Confianza: {result.confidence_score}")
            print(f"   - Contenido OCR: {str(result.ocr_contents)[:200]}...")
        except Exception as e:
            print(f"❌ Error en OCR estructurado: {e}")
    else:
        print(f"⚠️ {test_file}: Archivo no encontrado para prueba")
    
    print()


def test_enhanced_ocr():
    """Test enhanced OCR functionality."""
    print("=== Prueba de OCR Mejorado ===")
    
    # Get API key
    api_key = input("Introduce tu API Key de Mistral para la prueba: ").strip()
    if not api_key:
        print("❌ Se necesita una API Key para probar el OCR mejorado.")
        return
    
    # Test with existing files
    test_files = ["ActadeNacimiento.pdf", "Cuenta.jpg"]
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"🔧 Procesando {test_file} con OCR mejorado...")
            try:
                # Test traditional OCR
                result_traditional = ocr_mistral_enhanced(test_file, api_key, use_structured=False)
                print(f"   📝 OCR tradicional: {len(result_traditional)} caracteres extraídos")
                
                # Test structured OCR
                result_structured = ocr_mistral_enhanced(test_file, api_key, use_structured=True)
                print(f"   🔬 OCR estructurado: {result_structured.file_name} procesado")
                print(f"      - Confianza: {result_structured.confidence_score}")
                
            except Exception as e:
                print(f"❌ Error procesando {test_file}: {e}")
        else:
            print(f"⚠️ {test_file}: Archivo no encontrado")
    
    print()


def test_zip_handler():
    """Test ZIP handler functionality."""
    print("=== Prueba de Manejador de ZIP ===")
    
    # Create a simple test to show ZIP handler capabilities
    zip_handler = ZipHandler()
    
    print(f"📦 Extensiones soportadas: {zip_handler.SUPPORTED_EXTENSIONS}")
    
    # Test file type detection
    test_files = [
        "documento.pdf",
        "imagen.jpg", 
        "archivo.zip",
        "texto.txt"
    ]
    
    for filename in test_files:
        is_supported = zip_handler.is_supported_file(filename)
        doc_type = zip_handler._detect_document_type(filename)
        print(f"   📄 {filename}: {'✅' if is_supported else '❌'} soportado, tipo: {doc_type}")
    
    print()


def create_sample_zip():
    """Create a sample ZIP file for testing (optional)."""
    print("=== Crear ZIP de Prueba ===")
    
    import zipfile
    
    # Check if we have files to zip
    available_files = []
    test_files = ["ActadeNacimiento.pdf", "NSS.pdf", "Cuenta.jpg"]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            available_files.append(file_path)
    
    if len(available_files) >= 2:
        zip_name = "expediente_prueba.zip"
        print(f"📦 Creando {zip_name} con {len(available_files)} archivos...")
        
        try:
            with zipfile.ZipFile(zip_name, 'w') as zipf:
                for file_path in available_files:
                    zipf.write(file_path, os.path.basename(file_path))
                    print(f"   ✅ Agregado: {file_path}")
            
            print(f"✅ ZIP creado exitosamente: {zip_name}")
            return zip_name
            
        except Exception as e:
            print(f"❌ Error creando ZIP: {e}")
            return None
    else:
        print("⚠️ No hay suficientes archivos para crear un ZIP de prueba")
        return None


def main():
    """Main test function."""
    print("🧪 PRUEBAS DE FUNCIONALIDAD ZIP Y OCR ESTRUCTURADO")
    print("=" * 60)
    print()
    
    # Run tests
    test_zip_detection()
    test_zip_handler()
    
    # Ask user what they want to test
    print("¿Qué pruebas quieres ejecutar?")
    print("1. OCR Estructurado")
    print("2. OCR Mejorado") 
    print("3. Crear ZIP de prueba")
    print("4. Todas las pruebas")
    print("0. Salir")
    
    choice = input("\nElige una opción (0-4): ").strip()
    
    if choice == "1":
        test_structured_ocr()
    elif choice == "2":
        test_enhanced_ocr()
    elif choice == "3":
        zip_file = create_sample_zip()
        if zip_file:
            print(f"\n📦 Puedes usar {zip_file} para probar la funcionalidad ZIP en la aplicación principal.")
    elif choice == "4":
        test_structured_ocr()
        test_enhanced_ocr()
        create_sample_zip()
    elif choice == "0":
        print("👋 ¡Hasta luego!")
        return
    else:
        print("❌ Opción no válida")
    
    print("\n✅ Pruebas completadas.")


if __name__ == "__main__":
    main()
