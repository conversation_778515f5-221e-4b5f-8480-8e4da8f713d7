#!/bin/bash

# Configurar colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 DESEMPAPELADOR RH - LANZADOR AUTOMÁTICO${NC}"
echo "=================================================="
echo

# Verificar que Python está instalado
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}❌ Error: Python no está instalado${NC}"
        echo
        echo -e "${YELLOW}🔧 Para solucionar:${NC}"
        echo "1. En macOS: brew install python3"
        echo "2. En Ubuntu/Debian: sudo apt install python3 python3-pip"
        echo "3. En CentOS/RHEL: sudo yum install python3 python3-pip"
        echo
        read -p "Presiona Enter para salir..."
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# Verificar que estamos en el directorio correcto
if [ ! -f "app.py" ]; then
    echo -e "${RED}❌ Error: No se encuentra app.py${NC}"
    echo "   Asegúrate de ejecutar este script desde la carpeta del proyecto"
    echo
    read -p "Presiona Enter para salir..."
    exit 1
fi

# Verificar si existe el entorno virtual
if [ -f "venv/bin/activate" ]; then
    echo -e "${YELLOW}🔧 Activando entorno virtual...${NC}"
    source venv/bin/activate
else
    echo -e "${YELLOW}⚠️  No se encontró entorno virtual, usando Python global${NC}"
fi

# Verificar dependencias básicas
echo -e "${BLUE}🔍 Verificando dependencias...${NC}"
$PYTHON_CMD -c "import streamlit" 2>/dev/null
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Streamlit no está instalado${NC}"
    echo -e "${YELLOW}🔧 Instalando dependencias...${NC}"
    
    # Verificar si pip está disponible
    if command -v pip3 &> /dev/null; then
        PIP_CMD="pip3"
    elif command -v pip &> /dev/null; then
        PIP_CMD="pip"
    else
        echo -e "${RED}❌ Error: pip no está instalado${NC}"
        read -p "Presiona Enter para salir..."
        exit 1
    fi
    
    $PIP_CMD install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Error al instalar dependencias${NC}"
        read -p "Presiona Enter para salir..."
        exit 1
    fi
fi

echo -e "${GREEN}✅ Dependencias verificadas${NC}"
echo
echo -e "${BLUE}🌐 Iniciando aplicación...${NC}"
echo -e "${BLUE}📱 La aplicación se abrirá automáticamente en tu navegador${NC}"
echo
echo -e "${YELLOW}⚠️  Para cerrar la aplicación, presiona Ctrl+C${NC}"
echo "=================================================="
echo

# Iniciar la aplicación
$PYTHON_CMD launcher.py

echo
read -p "Presiona Enter para salir..."
