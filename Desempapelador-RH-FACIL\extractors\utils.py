import re
import json

def extraer_json_de_llm(content):
    """
    Extrae el primer bloque JSON válido de un string, ignorando texto extra, backticks, etc.
    Útil para respuestas de LLM que devuelven el JSON envuelto en texto o markdown.

    Args:
        content (str): Respuesta cruda del LLM.
    Returns:
        dict: Diccionario extraído del bloque JSON, o vacío si falla.

    Ejemplo:
        >>> extraer_json_de_llm('Aquí está tu JSON:\n```json\n{"campo": "valor"}\n```')
        {'campo': 'valor'}
    """
    match = re.search(r'\{[\s\S]*\}', content)
    if match:
        try:
            return json.loads(match.group(0))
        except Exception as e:
            print(f"Error al parsear JSON: {e}")
            return {}
    return {} 