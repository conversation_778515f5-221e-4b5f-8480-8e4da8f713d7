# 🚀 Instalación Local - Desempapelador RH

Guía completa para instalar y ejecutar Desempapelador RH como aplicación local en Windows y ChromeOS/Google OS.

## 🪟 Windows - Instalación Súper Fácil

### Opción 1: Instalación Automática (Recomendada)

1. **Descargar Python** (si no lo tienes):
   - Ve a [python.org/downloads](https://python.org/downloads/)
   - Descarga la versión más reciente
   - **IMPORTANTE**: Durante la instalación, marca "Add Python to PATH"

2. **Instalar dependencias**:
   - Haz doble clic en `Instalar_Dependencias.bat`
   - Espera a que termine la instalación (puede tomar varios minutos)

3. **Ejecutar la aplicación**:
   - Haz doble clic en `Desempapelador.cmd`
   - La aplicación se abrirá automáticamente en tu navegador

### Opción 2: Instalación Manual

```bash
# 1. Crear entorno virtual (opcional pero recomendado)
python -m venv venv
venv\Scripts\activate

# 2. Instalar dependencias
pip install -r requirements.txt

# 3. Ejecutar aplicación
python launcher.py
```

## 🌐 ChromeOS/Google OS

### Preparación del Entorno Linux

1. **Activar Linux en ChromeOS**:
   - Ve a Configuración → Avanzado → Desarrolladores
   - Activa "Entorno de desarrollo de Linux"
   - Espera a que se instale

2. **Ejecutar la aplicación**:
   ```bash
   python3 iniciar_chromeos.py
   ```

El script se encargará automáticamente de:
- Instalar Python si no está disponible
- Instalar todas las dependencias
- Configurar el entorno
- Abrir la aplicación en Chrome

## 📁 Archivos de Lanzamiento

### Windows
- **`Desempapelador.cmd`** - Doble clic para ejecutar (más fácil)
- **`Iniciar_Desempapelador.bat`** - Lanzador completo con verificaciones
- **`Instalar_Dependencias.bat`** - Instalador automático de dependencias
- **`launcher.py`** - Lanzador Python multiplataforma

### ChromeOS/Linux
- **`iniciar_chromeos.py`** - Lanzador específico para ChromeOS
- **`launcher.py`** - Lanzador Python multiplataforma

## 🔧 Solución de Problemas

### Windows

#### "Python no está instalado"
- Descarga Python desde [python.org](https://python.org/downloads/)
- Durante la instalación, marca "Add Python to PATH"
- Reinicia el sistema

#### "Error al instalar dependencias"
- Ejecuta `Instalar_Dependencias.bat` como administrador
- Verifica tu conexión a internet
- Si persiste, instala manualmente:
  ```bash
  pip install streamlit pandas "mistralai[document]" pydantic
  ```

#### "El puerto 8501 está en uso"
- Cierra otras aplicaciones Streamlit
- Reinicia el sistema
- El launcher encontrará automáticamente otro puerto libre

### ChromeOS

#### "No se puede instalar Python"
- Asegúrate de que Linux esté activado en ChromeOS
- Ejecuta en terminal:
  ```bash
  sudo apt update
  sudo apt install python3 python3-pip
  ```

#### "Error de permisos"
- Algunos comandos requieren `sudo`
- El script te pedirá permisos cuando sea necesario

## 🌐 Uso de la Aplicación

1. **Inicio**: La aplicación se abre automáticamente en tu navegador
2. **URL**: Normalmente `http://localhost:8501`
3. **API Key**: ✅ **YA INCLUIDA** - No necesitas buscar ni ingresar nada
4. **Archivos**: Sube PDFs, imágenes o archivos ZIP
5. **Procesamiento**: La app procesa automáticamente todos los documentos
6. **Resultados**: Revisa y descarga el Excel consolidado

## 🔒 Privacidad y Seguridad

- ✅ **Completamente local**: Todos los datos se procesan en tu computadora
- ✅ **Sin almacenamiento**: Los archivos se eliminan después del procesamiento
- ✅ **API Key privada**: Tu clave se usa solo en tu sesión local
- ✅ **Sin conexiones externas**: Solo se conecta a Mistral para OCR

## 📋 Características Disponibles

### Formatos Soportados
- **Documentos**: PDF, JPG, PNG
- **Archivos comprimidos**: ZIP (con extracción automática)
- **ZIP anidados**: Soporta múltiples niveles

### Tipos de Documentos
- Acta de Nacimiento
- NSS (Número de Seguridad Social)
- Aviso de Retención
- Comprobante de Domicilio
- SAT (RFC)
- INE (Credencial de Elector)
- Cuenta Bancaria

### Funciones Avanzadas
- **OCR Estructurado**: Mejor precisión con metadatos
- **Procesamiento por lotes**: Múltiples archivos simultáneamente
- **Consolidación automática**: Agrupa documentos por empleado
- **Exportación Excel**: Resultados listos para usar

## 🆘 Soporte

### Si algo no funciona:

1. **Verifica los requisitos**:
   - Python 3.7 o superior
   - Conexión a internet (para instalar dependencias)
   - API Key incluida automáticamente

2. **Ejecuta el diagnóstico**:
   ```bash
   python test_zip_functionality.py
   ```

3. **Reinstala dependencias**:
   - Windows: Ejecuta `Instalar_Dependencias.bat`
   - ChromeOS: Ejecuta `iniciar_chromeos.py`

4. **Verifica logs**:
   - Los errores se muestran en la ventana de comandos
   - No cierres la ventana para ver mensajes de error

## 💡 Consejos de Uso

### Para mejor rendimiento:
- Usa archivos ZIP para procesar múltiples documentos
- Activa "OCR Estructurado" para mejor precisión
- Nombra los archivos con ID de empleado al inicio

### Para evitar problemas:
- No cierres la ventana de comandos mientras usas la app
- Usa una conexión estable a internet
- La API Key ya está incluida - solo abre y usa

## 🔄 Actualizaciones

Para actualizar la aplicación:
1. Descarga la nueva versión
2. Reemplaza los archivos
3. Ejecuta `Instalar_Dependencias.bat` si hay nuevas dependencias

---

**¿Necesitas ayuda?** Revisa los mensajes en la ventana de comandos o ejecuta los scripts de diagnóstico incluidos.
