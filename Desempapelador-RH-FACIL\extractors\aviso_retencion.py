import json
from mistralai import Mistral
from extractors.utils import extraer_json_de_llm

CAMPOS = [
    "Nombre(s)",
    "Apellido_Paterno",
    "Apellido_Materno",
    "CURP",
    "RFC",
    "NSS",
    "Numero_Credito_Infonavit",
    "Tipo_Descuento",
    "Factor_Descuento"
]

PROMPT = (
    "A continuación tienes el texto extraído de un Aviso para Retención de Descuentos de Infonavit. "
    "Extrae SOLO los datos del trabajador y del crédito, ignorando los de la empresa y notas legales. "
    "Devuelve un JSON con los siguientes campos: "
    f"{CAMPOS}. "
    "Si algún campo no está presente, déjalo vacío. "
    "Ejemplo de formato de salida:\n"
    "{\n"
    "  \"Nombre(s)\": \"EFREN\",\n"
    "  \"Apellido_Paterno\": \"CRUZ\",\n"
    "  \"Apellido_Materno\": \"AVILA\",\n"
    "  \"CURP\": \"CUAE860802HJCRVF07\",\n"
    "  \"RFC\": \"CUAE860802178\",\n"
    "  \"NSS\": \"04058614522\",\n"
    "  \"Numero_Credito_Infonavit\": \"1414126135\",\n"
    "  \"Tipo_Descuento\": \"Cuota fija en pesos\",\n"
    "  \"Factor_Descuento\": \"2717.80\"\n"
    "}\n"
    "Texto extraído:\n"
)

def extract_aviso_retencion(texto_ocr, api_key):
    client = Mistral(api_key=api_key)
    response = client.chat.complete(
        model="mistral-small-latest",
        messages=[{"role": "user", "content": PROMPT + texto_ocr}],
        temperature=0.1,
        max_tokens=512
    )
    content = response.choices[0].message.content
    print("\n--- RESPUESTA CRUDA DEL LLM (AVISO RETENCIÓN) ---\n", content, "\n-----------------------------\n")
    return extraer_json_de_llm(content) 