# MixtralOCR - Expediente de Empleados Mexicanos 🚀

Aplicación avanzada en Streamlit (y CLI) para extraer información clave de documentos de empleados mexicanos (PDFs, imágenes y **archivos ZIP**) usando OCR y LLMs. Automatiza la captura de datos para Recursos Humanos con **soporte para archivos ZIP** y **OCR estructurado** basado en el cookbook de Mistral.

## 🆕 Nuevas Características

### ✨ Soporte para Archivos ZIP
- **Procesamiento automático de ZIP**: Extrae y procesa todos los documentos compatibles dentro de archivos ZIP
- **ZIP anidados**: Soporta archivos ZIP dentro de otros ZIP
- **Detección automática**: Identifica automáticamente archivos ZIP y los procesa recursivamente

### 🔬 OCR Estructurado (Basado en Mistral Cookbook)
- **Pydantic Models**: Utiliza modelos Pydantic para validación y estructura de datos
- **Extracción mejorada**: Mejor precisión en la extracción de datos estructurados
- **Metadatos enriquecidos**: Incluye información sobre temas, idioma y confianza

## 📋 Características Principales

- **Soporte para múltiples formatos**: PDF, imágenes (JPG, PNG) y **archivos ZIP**
- **Extracción automática de ZIP**: Procesa automáticamente todos los documentos dentro de archivos ZIP
- **OCR dual**: Mistral OCR tradicional y **OCR estructurado** con Pydantic
- **Extracción semántica**: Campos clave usando LLMs especializados
- **Revisión y corrección**: Interfaz para revisar y corregir resultados
- **Consolidación inteligente**: Agrupa documentos por empleado automáticamente
- **Soporte multiusuario**: Cada usuario usa su propia API Key de forma privada y aislada

## 📁 Formatos de Archivo Soportados

- **Documentos individuales**: PDF, JPG, JPEG, PNG
- **Archivos comprimidos**: ZIP (con extracción automática)
- **ZIP anidados**: Soporta múltiples niveles de compresión

## Tipos de documentos soportados

- Acta de nacimiento
- NSS (Número de Seguridad Social)
- Aviso de retención
- Comprobante de domicilio
- SAT (RFC)
- INE
- Cuenta bancaria

## 🛠️ Instalación

### 🚀 Instalación Local (Recomendada) - Sin Comandos

#### Windows - Súper Fácil
1. **Instalar dependencias**: Haz doble clic en `Instalar_Dependencias.bat`
2. **Ejecutar aplicación**: Haz doble clic en `Desempapelador.cmd`
3. ¡Listo! La aplicación se abre automáticamente en tu navegador

#### ChromeOS/Google OS
1. Activa Linux en ChromeOS (Configuración → Avanzado → Desarrolladores)
2. Ejecuta: `python3 iniciar_chromeos.py`
3. El script instala todo automáticamente

📖 **Guía completa**: Ver [INSTALACION_LOCAL.md](INSTALACION_LOCAL.md)

### 🔧 Instalación Manual (Desarrolladores)

1. Clona el repositorio y entra a la carpeta del proyecto.
2. Instala las dependencias:
   ```bash
   pip install -r requirements.txt
   ```
3. (Opcional para CLI) Crea un archivo `.env` en la raíz con tu API Key de Mistral:
   ```
   MISTRAL_API_KEY=tu_api_key_aqui
   ```

## Uso

### 🌐 Web (Streamlit) - Recomendado

1. Ejecuta la app web:
   ```bash
   streamlit run app.py
   ```
2. Ingresa tu API Key de Mistral en el campo seguro del sidebar (no se almacena, solo se usa en tu sesión).
3. **🆕 Sube archivos individuales o ZIP** - La app detecta automáticamente archivos ZIP
4. **🆕 Activa "OCR Estructurado"** si deseas mejor precisión y metadatos
5. Revisa, corrige y descarga el Excel consolidado.

**Nuevas funciones en la interfaz web:**
- ✅ Soporte para cargar archivos ZIP
- ✅ Información automática sobre archivos ZIP detectados
- ✅ Opción de OCR estructurado experimental
- ✅ Procesamiento automático de contenidos ZIP

#### **Recomendación para nombrar archivos**

Para facilitar la identificación automática de empleados y documentos:

- Usa un **ID de empleado corto y único** al inicio del nombre del archivo (ejemplo: 12345, abc01, etc.)
- Luego, agrega el tipo de documento (acta, nss, curp, ine, etc.)
- Ejemplo: `12345_acta.pdf`, `12345_nss.pdf`, `abc01_ine.jpg`

Esto ayuda a agrupar y vincular los documentos correctamente.

### 💻 CLI

1. Ejecuta la app CLI:
   ```bash
   python main.py
   ```
2. Ingresa tu API Key cuando se te solicite.
3. **🆕 Elige si usar OCR estructurado** cuando se te pregunte
4. Los archivos ZIP se procesan automáticamente

### 🧪 Pruebas y Diagnóstico

#### Verificar Instalación
```bash
python diagnostico.py
```
Este script verifica:
- Instalación de Python y dependencias
- Archivos del proyecto
- Conectividad de red
- Permisos y puertos

#### Probar Funcionalidades
```bash
python test_zip_functionality.py
```
Este script te permite probar:
- Detección de archivos ZIP
- OCR estructurado
- OCR mejorado
- Creación de ZIP de prueba

### 📁 Archivos de Lanzamiento Local

#### Windows
- **`Desempapelador.cmd`** - ⭐ Doble clic para ejecutar (más fácil)
- **`Iniciar_Desempapelador.bat`** - Lanzador completo con verificaciones
- **`Instalar_Dependencias.bat`** - Instalador automático
- **`launcher.py`** - Lanzador Python multiplataforma

#### ChromeOS/Linux
- **`iniciar_chromeos.py`** - Lanzador específico para ChromeOS
- **`launcher.py`** - Lanzador Python multiplataforma

#### Diagnóstico
- **`diagnostico.py`** - Verificación completa del sistema
- **`ejemplo_zip.py`** - Ejemplos de uso de nuevas funciones

## Flujo de datos

1. El usuario sube archivos.
2. Se aplica OCR (Mistral o PyMuPDF).
3. Se detecta el tipo de documento.
4. Se extraen campos clave con LLM.
5. El usuario revisa y corrige.
6. Se consolidan los datos por empleado.
7. Se pueden guardar ejemplos para entrenamiento futuro.

## Consideraciones de privacidad

- **Cada usuario usa su propia API Key, que nunca se almacena ni comparte.**
- Los archivos y datos procesados solo existen durante la sesión y no se guardan en el servidor.
- No subas documentos reales sin consentimiento.
- Los datos procesados pueden contener información sensible.
- Si recibes donaciones y el proyecto crece, considera implementar almacenamiento seguro y manejo de consentimiento.

## 🔧 Nuevas Funciones de API

### OCR Mejorado
```python
from ocr import ocr_mistral_enhanced

# Procesar archivo individual o ZIP
result = ocr_mistral_enhanced("documento.pdf", api_key, use_structured=True)

# Para ZIP, retorna lista de resultados
zip_results = ocr_mistral_enhanced("expediente.zip", api_key)
```

### OCR Estructurado
```python
from extractors.structured_ocr import structured_ocr

# OCR con estructura Pydantic
structured_result = structured_ocr("documento.pdf", api_key)
print(structured_result.topics)  # Lista de temas
print(structured_result.confidence_score)  # Puntuación de confianza
```

### Manejo de ZIP
```python
from utils.zip_handler import ZipHandler

handler = ZipHandler()
extracted_files = handler.process_zip_recursively("expediente.zip")
```

## 📂 Estructura del proyecto

```
├── app.py                      # Interfaz web Streamlit (actualizada con ZIP)
├── main.py                     # Script principal (actualizado con ZIP)
├── ocr.py                      # Módulo OCR mejorado con ZIP y estructurado
├── extraction.py               # Orquestación de extracción semántica
├── test_zip_functionality.py   # 🆕 Script de pruebas para nuevas funciones
├── extractors/                 # Extractores específicos por tipo de documento
│   ├── structured_ocr.py       # 🆕 OCR estructurado con Pydantic
│   ├── utils.py                # Utilidades generales
│   ├── acta.py
│   ├── nss.py
│   └── ...
├── utils/                      # 🆕 Utilidades
│   ├── __init__.py
│   └── zip_handler.py          # 🆕 Manejador de archivos ZIP
└── requirements.txt            # Dependencias actualizadas
```

## Ejemplo de cómo agregar un nuevo extractor

1. Crea un archivo en `extractors/` (por ejemplo, `extractors/recibo_nomina.py`).
2. Define la función `extract_recibo_nomina(texto: str, api_key: str) -> dict`.
3. Agrega el extractor en `extraction.py` para que sea detectado.

## Despliegue en Streamlit Cloud

1. Sube el repositorio a GitHub.
2. Ve a [streamlit.io/cloud](https://streamlit.io/cloud) y conecta tu repo.
3. Selecciona `app.py` como archivo principal.
4. ¡Listo! Cada usuario podrá usar su propia API Key y procesar sus archivos de forma privada.

## 🔄 Mejoras Implementadas

Basadas en el cookbook de Mistral OCR:

1. **Estructura de datos mejorada**: Uso de Pydantic para validación y tipado fuerte
2. **Mejor manejo de errores**: Reintentos automáticos y fallbacks robustos
3. **Metadatos enriquecidos**: Información sobre confianza, temas e idioma detectado
4. **Procesamiento por lotes**: Soporte nativo para múltiples archivos y ZIP
5. **Gestión de memoria**: Limpieza automática de archivos temporales
6. **Logging mejorado**: Mejor trazabilidad y debugging

## 📝 Ejemplos de Uso

### Procesar un ZIP con múltiples expedientes
1. Crea un ZIP con documentos de empleados
2. Sube el ZIP a la interfaz web
3. Activa "OCR Estructurado" si deseas mejor precisión
4. El sistema extraerá y procesará automáticamente todos los documentos

### Usar OCR estructurado para mejor precisión
1. En la interfaz web, marca "🔬 Usar OCR Estructurado"
2. Los resultados incluirán metadatos adicionales y mejor estructura

## Contribuciones

¡Bienvenidas! Abre un issue o un pull request.

## Licencia

MIT (o la que prefieras)

---

**No subas tu archivo `.env` a repositorios públicos.** 