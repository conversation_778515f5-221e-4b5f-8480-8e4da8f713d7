#!/usr/bin/env python3
"""
Script para crear un paquete de distribución listo para compartir.
Incluye solo los archivos necesarios y crea instrucciones para el usuario.
"""

import os
import shutil
import zipfile
from pathlib import Path
from datetime import datetime


def crear_leeme():
    """Crea el archivo LEEME_PRIMERO.txt con instrucciones."""
    contenido = """================================================================
                    DESEMPAPELADOR RH
              INSTRUCCIONES DE USO RAPIDO
================================================================

=== INSTALACION SUPER FACIL ===

WINDOWS:
1. Haz doble clic en "Instalar_Dependencias.bat"
   (Esto instala todo automaticamente - puede tomar unos minutos)

2. Haz doble clic en "Desempapelador.cmd"
   (La aplicacion se abre automaticamente en tu navegador)

3. Listo! Ingresa tu API Key de Mistral y comienza a usar

CHROMEOS/GOOGLE OS:
1. Activa Linux en ChromeOS (Configuracion → Avanzado → Desarrolladores)
2. Abre Terminal y navega a esta carpeta
3. Ejecuta: python3 iniciar_chromeos.py

=== REQUISITOS ===
- Windows 10/11 o ChromeOS con Linux
- Conexion a internet (solo para instalar dependencias)
- API Key YA INCLUIDA (no necesitas buscar una)

=== FUNCIONES PRINCIPALES ===
✓ Procesa PDFs, imagenes y archivos ZIP
✓ OCR tradicional y estructurado
✓ Extraccion automatica de datos de empleados
✓ Consolidacion por empleado
✓ Exportacion a Excel

=== TIPOS DE DOCUMENTOS SOPORTADOS ===
- Acta de Nacimiento
- NSS (Numero de Seguridad Social)
- Aviso de Retencion
- Comprobante de Domicilio
- SAT (RFC)
- INE (Credencial de Elector)
- Cuenta Bancaria

=== COMO USAR ===
1. Abre la aplicacion (doble clic en Desempapelador.cmd)
2. Ingresa tu API Key de Mistral en la barra lateral
3. Sube tus archivos (PDFs, imagenes o ZIP)
4. La aplicacion procesa automaticamente
5. Revisa los resultados y descarga el Excel

=== ARCHIVOS ZIP ===
- Puedes subir archivos ZIP con multiples documentos
- La aplicacion extrae y procesa automaticamente todo el contenido
- Soporta ZIP dentro de ZIP (anidados)
- Detecta automaticamente el tipo de cada documento

=== SOPORTE ===
- Si algo no funciona, ejecuta "diagnostico.py"
- Lee "INSTALACION_LOCAL.md" para guia completa
- Lee "README.md" para documentacion tecnica

=== PRIVACIDAD Y SEGURIDAD ===
✓ 100% Local - tus archivos no salen de tu computadora
✓ Sin almacenamiento - los archivos se eliminan despues del uso
✓ API Key privada - solo se usa en tu sesion local
✓ Sin servidores - solo se conecta a Mistral para OCR

=== SOLUCION DE PROBLEMAS COMUNES ===

PROBLEMA: "Python no esta instalado"
SOLUCION: Descarga Python desde python.org y marca "Add to PATH"

PROBLEMA: "Error al instalar dependencias"
SOLUCION: Ejecuta como administrador o verifica conexion a internet

PROBLEMA: "No se abre el navegador"
SOLUCION: Abre manualmente http://localhost:8501

PROBLEMA: "Puerto en uso"
SOLUCION: Cierra otras aplicaciones Streamlit o reinicia

=== CONSEJOS DE USO ===
- Usa archivos ZIP para procesar multiples expedientes
- Nombra los archivos con el ID del empleado al inicio
- Activa "OCR Estructurado" para mejor precision
- No cierres la ventana de comandos mientras usas la app
- Ten tu API Key lista antes de empezar

=== CONTACTO ===
Desarrollado con amor por Efren
Email: <EMAIL>

Gracias por usar Desempapelador RH!

================================================================
"""
    
    with open("LEEME_PRIMERO.txt", "w", encoding="utf-8") as f:
        f.write(contenido)


def obtener_archivos_esenciales():
    """Lista de archivos esenciales para la distribución."""
    archivos_esenciales = [
        # Archivos principales de lanzamiento
        "Desempapelador.cmd",
        "Iniciar_Desempapelador.bat", 
        "Instalar_Dependencias.bat",
        "launcher.py",
        
        # Aplicación principal
        "app.py",
        "main.py",
        "ocr.py",
        "extraction.py",
        "requirements.txt",
        
        # Documentación
        "README.md",
        "INSTALACION_LOCAL.md",
        
        # Scripts adicionales
        "iniciar_chromeos.py",
        "diagnostico.py",
        "ejemplo_zip.py",
        "test_zip_functionality.py",
        
        # Archivo de instrucciones
        "LEEME_PRIMERO.txt"
    ]
    
    # Directorios esenciales
    directorios_esenciales = [
        "extractors",
        "utils"
    ]
    
    return archivos_esenciales, directorios_esenciales


def crear_directorio_distribucion():
    """Crea el directorio de distribución."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    dir_nombre = f"Desempapelador-RH_v{timestamp}"
    
    if os.path.exists(dir_nombre):
        shutil.rmtree(dir_nombre)
    
    os.makedirs(dir_nombre)
    return dir_nombre


def copiar_archivos(dir_destino):
    """Copia los archivos esenciales al directorio de distribución."""
    archivos, directorios = obtener_archivos_esenciales()
    
    print("📁 Copiando archivos esenciales...")
    
    # Copiar archivos individuales
    for archivo in archivos:
        if os.path.exists(archivo):
            shutil.copy2(archivo, dir_destino)
            print(f"  ✅ {archivo}")
        else:
            print(f"  ⚠️  {archivo} - No encontrado")
    
    # Copiar directorios
    for directorio in directorios:
        if os.path.exists(directorio):
            dest_dir = os.path.join(dir_destino, directorio)
            shutil.copytree(directorio, dest_dir)
            print(f"  ✅ {directorio}/ (directorio)")
        else:
            print(f"  ⚠️  {directorio}/ - No encontrado")


def limpiar_archivos_innecesarios(dir_destino):
    """Elimina archivos innecesarios del paquete de distribución."""
    print("🧹 Limpiando archivos innecesarios...")
    
    # Patrones de archivos a eliminar
    patrones_eliminar = [
        "**/__pycache__",
        "**/*.pyc",
        "**/*.pyo", 
        "**/.DS_Store",
        "**/Thumbs.db",
        "**/*.tmp"
    ]
    
    for patron in patrones_eliminar:
        for archivo in Path(dir_destino).glob(patron):
            if archivo.is_file():
                archivo.unlink()
                print(f"  🗑️  {archivo.relative_to(dir_destino)}")
            elif archivo.is_dir():
                shutil.rmtree(archivo)
                print(f"  🗑️  {archivo.relative_to(dir_destino)}/")


def crear_zip(dir_origen):
    """Crea un archivo ZIP del paquete de distribución."""
    zip_nombre = f"{dir_origen}.zip"
    
    print(f"📦 Creando archivo ZIP: {zip_nombre}")
    
    with zipfile.ZipFile(zip_nombre, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(dir_origen):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, os.path.dirname(dir_origen))
                zipf.write(file_path, arc_name)
                print(f"  📄 {arc_name}")
    
    return zip_nombre


def mostrar_resumen(dir_distribucion, zip_archivo):
    """Muestra un resumen del paquete creado."""
    print("\n" + "="*60)
    print("✅ PAQUETE DE DISTRIBUCIÓN CREADO")
    print("="*60)
    
    print(f"\n📁 Directorio: {dir_distribucion}")
    print(f"📦 Archivo ZIP: {zip_archivo}")
    
    # Contar archivos
    total_archivos = sum([len(files) for r, d, files in os.walk(dir_distribucion)])
    tamaño_zip = os.path.getsize(zip_archivo) / (1024*1024)  # MB
    
    print(f"\n📊 Estadísticas:")
    print(f"   - Total de archivos: {total_archivos}")
    print(f"   - Tamaño del ZIP: {tamaño_zip:.1f} MB")
    
    print(f"\n🚀 Para distribuir:")
    print(f"   1. Comparte el archivo: {zip_archivo}")
    print(f"   2. O comparte la carpeta: {dir_distribucion}")
    
    print(f"\n💡 Instrucciones para el usuario:")
    print(f"   1. Extraer el ZIP")
    print(f"   2. Leer LEEME_PRIMERO.txt")
    print(f"   3. Ejecutar Instalar_Dependencias.bat (Windows)")
    print(f"   4. Ejecutar Desempapelador.cmd (Windows)")


def main():
    """Función principal del empaquetador."""
    print("📦 CREADOR DE PAQUETE DE DISTRIBUCIÓN")
    print("="*50)
    print()
    
    # Verificar que estamos en el directorio correcto
    if not os.path.exists("app.py"):
        print("❌ Error: No se encuentra app.py")
        print("   Ejecuta este script desde la carpeta del proyecto")
        return
    
    try:
        # Crear archivo de instrucciones
        print("📝 Creando archivo de instrucciones...")
        crear_leeme()
        
        # Crear directorio de distribución
        dir_distribucion = crear_directorio_distribucion()
        print(f"📁 Directorio de distribución: {dir_distribucion}")
        
        # Copiar archivos esenciales
        copiar_archivos(dir_distribucion)
        
        # Limpiar archivos innecesarios
        limpiar_archivos_innecesarios(dir_distribucion)
        
        # Crear ZIP
        zip_archivo = crear_zip(dir_distribucion)
        
        # Mostrar resumen
        mostrar_resumen(dir_distribucion, zip_archivo)
        
    except Exception as e:
        print(f"❌ Error creando paquete: {e}")
    
    print("\n" + "="*50)
    input("Presiona Enter para salir...")


if __name__ == "__main__":
    main()
