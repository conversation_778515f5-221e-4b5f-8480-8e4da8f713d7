================================================================
                    DESEMPAPELADOR RH
              INSTRUCCIONES DE USO RAPIDO
================================================================

=== INSTALACION SUPER FACIL ===

WINDOWS:
1. Haz doble clic en "Instalar_Dependencias.bat"
   (Esto instala todo automaticamente - puede tomar unos minutos)

2. Haz doble clic en "Desempapelador.cmd"
   (La aplicacion se abre automaticamente en tu navegador)

3. Listo! Ingresa tu API Key de Mistral y comienza a usar

CHROMEOS/GOOGLE OS:
1. Activa Linux en ChromeOS (Configuracion → Avanzado → Desarrolladores)
2. Abre Terminal y navega a esta carpeta
3. Ejecuta: python3 iniciar_chromeos.py

=== REQUISITOS ===
- Windows 10/11 o ChromeOS con Linux
- Conexion a internet (solo para instalar dependencias)
- API Key de Mistral (se obtiene gratis en mistral.ai)

=== FUNCIONES PRINCIPALES ===
✓ Procesa PDFs, imagenes y archivos ZIP
✓ OCR tradicional y estructurado
✓ Extraccion automatica de datos de empleados
✓ Consolidacion por empleado
✓ Exportacion a Excel

=== TIPOS DE DOCUMENTOS SOPORTADOS ===
- Acta de Nacimiento
- NSS (Numero de Seguridad Social)
- Aviso de Retencion
- Comprobante de Domicilio
- SAT (RFC)
- INE (Credencial de Elector)
- Cuenta Bancaria

=== COMO USAR ===
1. Abre la aplicacion (doble clic en Desempapelador.cmd)
2. Ingresa tu API Key de Mistral en la barra lateral
3. Sube tus archivos (PDFs, imagenes o ZIP)
4. La aplicacion procesa automaticamente
5. Revisa los resultados y descarga el Excel

=== ARCHIVOS ZIP ===
- Puedes subir archivos ZIP con multiples documentos
- La aplicacion extrae y procesa automaticamente todo el contenido
- Soporta ZIP dentro de ZIP (anidados)
- Detecta automaticamente el tipo de cada documento

=== SOPORTE ===
- Si algo no funciona, ejecuta "diagnostico.py"
- Lee "INSTALACION_LOCAL.md" para guia completa
- Lee "README.md" para documentacion tecnica

=== PRIVACIDAD Y SEGURIDAD ===
✓ 100% Local - tus archivos no salen de tu computadora
✓ Sin almacenamiento - los archivos se eliminan despues del uso
✓ API Key privada - solo se usa en tu sesion local
✓ Sin servidores - solo se conecta a Mistral para OCR

=== SOLUCION DE PROBLEMAS COMUNES ===

PROBLEMA: "Python no esta instalado"
SOLUCION: Descarga Python desde python.org y marca "Add to PATH"

PROBLEMA: "Error al instalar dependencias"
SOLUCION: Ejecuta como administrador o verifica conexion a internet

PROBLEMA: "No se abre el navegador"
SOLUCION: Abre manualmente http://localhost:8501

PROBLEMA: "Puerto en uso"
SOLUCION: Cierra otras aplicaciones Streamlit o reinicia

=== CONSEJOS DE USO ===
- Usa archivos ZIP para procesar multiples expedientes
- Nombra los archivos con el ID del empleado al inicio
- Activa "OCR Estructurado" para mejor precision
- No cierres la ventana de comandos mientras usas la app
- Ten tu API Key lista antes de empezar

=== CONTACTO ===
Desarrollado con amor por Efren
Email: <EMAIL>

Gracias por usar Desempapelador RH!

================================================================
