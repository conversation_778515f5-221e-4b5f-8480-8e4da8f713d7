#!/usr/bin/env python3
"""
Ejemplo simple de cómo usar las nuevas funcionalidades de ZIP y OCR estructurado.
"""

import os
from ocr import ocr_mistral_enhanced, is_zip_file
from extractors.structured_ocr import structured_ocr


def ejemplo_basico():
    """Ejemplo básico de uso de las nuevas funcionalidades."""
    
    print("🚀 EJEMPLO DE USO - ZIP Y OCR ESTRUCTURADO")
    print("=" * 50)
    
    # Solicitar API Key
    api_key = input("Introduce tu API Key de Mistral: ").strip()
    if not api_key:
        print("❌ Se necesita una API Key para continuar.")
        return
    
    # Buscar archivos de ejemplo
    archivos_ejemplo = [
        "ActadeNacimiento.pdf",
        "NSS.pdf", 
        "Cuenta.jpg",
        "expediente_prueba.zip"  # Si existe
    ]
    
    archivos_disponibles = [f for f in archivos_ejemplo if os.path.exists(f)]
    
    if not archivos_disponibles:
        print("⚠️ No se encontraron archivos de ejemplo.")
        print("Archivos buscados:", archivos_ejemplo)
        return
    
    print(f"📄 Archivos encontrados: {len(archivos_disponibles)}")
    for archivo in archivos_disponibles:
        print(f"   - {archivo}")
    
    print("\n" + "=" * 50)
    
    # Procesar cada archivo
    for archivo in archivos_disponibles:
        print(f"\n🔍 Procesando: {archivo}")
        
        # Verificar si es ZIP
        if is_zip_file(archivo):
            print("   📦 Es un archivo ZIP - procesando contenido...")
            try:
                resultados = ocr_mistral_enhanced(archivo, api_key, use_structured=False)
                print(f"   ✅ Extraídos {len(resultados)} archivos del ZIP")
                
                for i, resultado in enumerate(resultados[:3]):  # Mostrar solo los primeros 3
                    if resultado['success']:
                        print(f"      {i+1}. {resultado['file_name']} ({resultado['document_type']})")
                        texto = resultado.get('text_content', '')
                        print(f"         Texto: {texto[:100]}...")
                    else:
                        print(f"      {i+1}. {resultado['file_name']} - Error: {resultado['error']}")
                
                if len(resultados) > 3:
                    print(f"      ... y {len(resultados) - 3} archivos más")
                    
            except Exception as e:
                print(f"   ❌ Error procesando ZIP: {e}")
        
        else:
            print("   📄 Es un archivo individual")
            
            # Ejemplo con OCR tradicional
            print("   🔧 Probando OCR tradicional...")
            try:
                texto_tradicional = ocr_mistral_enhanced(archivo, api_key, use_structured=False)
                print(f"      ✅ Extraído: {len(texto_tradicional)} caracteres")
                print(f"      Muestra: {texto_tradicional[:150]}...")
            except Exception as e:
                print(f"      ❌ Error en OCR tradicional: {e}")
            
            # Ejemplo con OCR estructurado
            print("   🔬 Probando OCR estructurado...")
            try:
                resultado_estructurado = structured_ocr(archivo, api_key)
                print(f"      ✅ Archivo: {resultado_estructurado.file_name}")
                print(f"      📋 Temas: {resultado_estructurado.topics}")
                print(f"      🌐 Idioma: {resultado_estructurado.languages}")
                print(f"      📊 Confianza: {resultado_estructurado.confidence_score}")
                print(f"      📄 Tipo: {resultado_estructurado.document_type}")
                
                # Mostrar una muestra del contenido estructurado
                contenido = str(resultado_estructurado.ocr_contents)
                print(f"      📝 Contenido: {contenido[:200]}...")
                
            except Exception as e:
                print(f"      ❌ Error en OCR estructurado: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Ejemplo completado.")
    print("\n💡 Consejos:")
    print("   - Usa archivos ZIP para procesar múltiples documentos de una vez")
    print("   - El OCR estructurado proporciona mejor metadata y estructura")
    print("   - Los archivos ZIP pueden contener otros ZIP (procesamiento recursivo)")


def crear_zip_ejemplo():
    """Crea un ZIP de ejemplo si hay archivos disponibles."""
    
    import zipfile
    
    print("📦 CREAR ZIP DE EJEMPLO")
    print("=" * 30)
    
    # Buscar archivos para incluir en el ZIP
    archivos_candidatos = [
        "ActadeNacimiento.pdf",
        "NSS.pdf",
        "Cuenta.jpg",
        "INE.pdf",
        "SAT.pdf"
    ]
    
    archivos_disponibles = [f for f in archivos_candidatos if os.path.exists(f)]
    
    if len(archivos_disponibles) < 2:
        print("⚠️ Se necesitan al menos 2 archivos para crear un ZIP de ejemplo.")
        print(f"Archivos encontrados: {archivos_disponibles}")
        return None
    
    zip_nombre = "expediente_ejemplo.zip"
    
    try:
        with zipfile.ZipFile(zip_nombre, 'w') as zipf:
            for archivo in archivos_disponibles:
                zipf.write(archivo, archivo)
                print(f"   ✅ Agregado: {archivo}")
        
        print(f"📦 ZIP creado: {zip_nombre}")
        print(f"   Contiene {len(archivos_disponibles)} archivos")
        return zip_nombre
        
    except Exception as e:
        print(f"❌ Error creando ZIP: {e}")
        return None


def main():
    """Función principal del ejemplo."""
    
    print("¿Qué quieres hacer?")
    print("1. Ejecutar ejemplo completo")
    print("2. Crear ZIP de ejemplo")
    print("3. Ambos")
    print("0. Salir")
    
    opcion = input("\nElige una opción (0-3): ").strip()
    
    if opcion == "1":
        ejemplo_basico()
    elif opcion == "2":
        zip_creado = crear_zip_ejemplo()
        if zip_creado:
            print(f"\n💡 Ahora puedes usar {zip_creado} en la aplicación principal.")
    elif opcion == "3":
        zip_creado = crear_zip_ejemplo()
        if zip_creado:
            print(f"\n💡 ZIP creado: {zip_creado}")
        print("\n" + "=" * 50)
        ejemplo_basico()
    elif opcion == "0":
        print("👋 ¡Hasta luego!")
    else:
        print("❌ Opción no válida")


if __name__ == "__main__":
    main()
